<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Treasury extends Model
{
    protected $fillable = [
        'name',
        'name_ar',
        'office_id',
        'description',
        'current_balance',
        'is_active',
    ];

    protected $casts = [
        'current_balance' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function office(): BelongsTo
    {
        return $this->belongsTo(Office::class);
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function receipts(): HasMany
    {
        return $this->hasMany(Receipt::class);
    }

    public function treasuryTransactions(): HasMany
    {
        return $this->hasMany(TreasuryTransaction::class);
    }

    public function electronicPayments(): HasMany
    {
        return $this->hasMany(ElectronicPayment::class);
    }

    public function prePrintedReceiptSeries(): HasMany
    {
        return $this->hasMany(PrePrintedReceiptSeries::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForOffice($query, $officeId)
    {
        return $query->where('office_id', $officeId);
    }

    public function scopeForDepartment($query, $departmentId)
    {
        return $query->whereHas('office', function ($q) use ($departmentId) {
            $q->where('department_id', $departmentId);
        });
    }

    // Helper methods
    public function updateBalance($amount, $type = 'add'): void
    {
        if ($type === 'add') {
            $this->increment('current_balance', $amount);
        } else {
            $this->decrement('current_balance', $amount);
        }
    }

    public function getDepartment()
    {
        return $this->office->department;
    }
}
