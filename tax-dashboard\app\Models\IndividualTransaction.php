<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class IndividualTransaction extends Model
{
    protected $fillable = [
        'department_id',
        'office_id',
        'transaction_type_id',
        'user_id',
        'reference_number',
        'individual_name',
        'national_id',
        'phone',
        'address',
        'amount',
        'status',
        'notes',
        'dynamic_fields',
        'transaction_date',
        'submitted_at',
        'completed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'dynamic_fields' => 'array',
        'transaction_date' => 'date',
        'submitted_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    // Relationships
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function office(): BelongsTo
    {
        return $this->belongsTo(Office::class);
    }

    public function transactionType(): BelongsTo
    {
        return $this->belongsTo(TransactionType::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeSubmitted($query)
    {
        return $query->where('status', 'submitted');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('transaction_date', today());
    }

    // Helper methods
    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'draft' => 'مسودة',
            'submitted' => 'مقدمة',
            'completed' => 'مكتملة',
            'cancelled' => 'ملغية',
            default => $this->status,
        };
    }
}
