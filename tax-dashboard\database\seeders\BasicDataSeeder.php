<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Department;
use App\Models\Office;
use App\Models\TaxType;
use App\Models\ActivityType;
use App\Models\LegalForm;
use App\Models\TransactionType;
use App\Models\Treasury;
use App\Models\User;

class BasicDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Tax Types
        $taxTypes = [
            ['name' => 'Professional Tax', 'name_ar' => 'ضريبة على المهن الحرة'],
            ['name' => 'Trade and Industry Income Tax', 'name_ar' => 'ضريبة دخل تجارة وصناعة وحرف'],
            ['name' => 'Corporate Income Tax', 'name_ar' => 'ضريبة دخل الشركات'],
            ['name' => 'Salary and Wages Tax', 'name_ar' => 'ضريبة على الأجور والمرتبات'],
        ];

        foreach ($taxTypes as $taxType) {
            TaxType::create($taxType);
        }

        // Create Legal Forms
        $legalForms = [
            ['name' => 'Public Sector', 'name_ar' => 'قطاع عام'],
            ['name' => 'Joint Stock Company', 'name_ar' => 'شركة مساهمة'],
            ['name' => 'Individual', 'name_ar' => 'فردي'],
            ['name' => 'Foreign Company', 'name_ar' => 'شركة أجنبية'],
            ['name' => 'Limited Liability Company', 'name_ar' => 'شركة ذات مسؤولية محدودة'],
            ['name' => 'Partnership', 'name_ar' => 'شركة تضامن'],
        ];

        foreach ($legalForms as $legalForm) {
            LegalForm::create($legalForm);
        }

        // Create Departments
        $departments = [
            ['name' => 'Central Tax Department', 'name_ar' => 'الإدارة الضريبية المركزية'],
            ['name' => 'North Tax Department', 'name_ar' => 'الإدارة الضريبية الشمالية'],
            ['name' => 'South Tax Department', 'name_ar' => 'الإدارة الضريبية الجنوبية'],
            ['name' => 'East Tax Department', 'name_ar' => 'الإدارة الضريبية الشرقية'],
            ['name' => 'West Tax Department', 'name_ar' => 'الإدارة الضريبية الغربية'],
        ];

        foreach ($departments as $departmentData) {
            $department = Department::create($departmentData);

            // Create offices for each department
            $offices = [
                ['name' => $departmentData['name'] . ' - Main Office', 'name_ar' => $departmentData['name_ar'] . ' - المكتب الرئيسي'],
                ['name' => $departmentData['name'] . ' - Branch Office 1', 'name_ar' => $departmentData['name_ar'] . ' - الفرع الأول'],
                ['name' => $departmentData['name'] . ' - Branch Office 2', 'name_ar' => $departmentData['name_ar'] . ' - الفرع الثاني'],
            ];

            foreach ($offices as $officeData) {
                $office = Office::create([
                    'name' => $officeData['name'],
                    'name_ar' => $officeData['name_ar'],
                    'department_id' => $department->id,
                    'address' => 'Sample Address for ' . $officeData['name'],
                    'phone' => '+20-1234567890',
                ]);

                // Create treasuries for each office
                $treasuries = [
                    ['name' => 'Main Treasury', 'name_ar' => 'الخزينة الرئيسية'],
                    ['name' => 'Secondary Treasury', 'name_ar' => 'الخزينة الفرعية'],
                ];

                foreach ($treasuries as $treasuryData) {
                    Treasury::create([
                        'name' => $treasuryData['name'],
                        'name_ar' => $treasuryData['name_ar'],
                        'office_id' => $office->id,
                        'current_balance' => 0,
                    ]);
                }
            }
        }

        // Create Activity Types
        $professionalTaxType = TaxType::where('name_ar', 'ضريبة على المهن الحرة')->first();
        $tradeTaxType = TaxType::where('name_ar', 'ضريبة دخل تجارة وصناعة وحرف')->first();
        $corporateTaxType = TaxType::where('name_ar', 'ضريبة دخل الشركات')->first();
        $salaryTaxType = TaxType::where('name_ar', 'ضريبة على الأجور والمرتبات')->first();

        $activityTypes = [
            ['name' => 'Medical Practice', 'name_ar' => 'ممارسة طبية', 'tax_type_id' => $professionalTaxType->id],
            ['name' => 'Legal Practice', 'name_ar' => 'ممارسة قانونية', 'tax_type_id' => $professionalTaxType->id],
            ['name' => 'Engineering Consultancy', 'name_ar' => 'استشارات هندسية', 'tax_type_id' => $professionalTaxType->id],
            ['name' => 'Retail Trade', 'name_ar' => 'تجارة تجزئة', 'tax_type_id' => $tradeTaxType->id],
            ['name' => 'Manufacturing', 'name_ar' => 'تصنيع', 'tax_type_id' => $tradeTaxType->id],
            ['name' => 'Construction', 'name_ar' => 'إنشاءات', 'tax_type_id' => $tradeTaxType->id],
            ['name' => 'Corporate Services', 'name_ar' => 'خدمات شركات', 'tax_type_id' => $corporateTaxType->id],
            ['name' => 'Financial Services', 'name_ar' => 'خدمات مالية', 'tax_type_id' => $corporateTaxType->id],
            ['name' => 'Employee Salaries', 'name_ar' => 'رواتب موظفين', 'tax_type_id' => $salaryTaxType->id],
        ];

        foreach ($activityTypes as $activityType) {
            ActivityType::create($activityType);
        }

        // Create Transaction Types with dynamic field configurations
        $transactionTypes = [
            [
                'name' => 'Standard Tax Payment',
                'name_ar' => 'دفع ضريبة عادية',
                'description' => 'Standard tax payment transaction',
                'default_value' => 100.00,
                'is_percentage' => false,
                'has_tax_file' => true,
                'has_start_date' => true,
                'has_end_date' => true,
                'shows_in_stamp_report' => true,
            ],
            [
                'name' => 'Contract Tax',
                'name_ar' => 'ضريبة عقد',
                'description' => 'Tax on contracts',
                'default_value' => 0.5,
                'is_percentage' => true,
                'has_contract' => true,
                'has_parties' => true,
                'has_first_party' => true,
                'has_second_party' => true,
                'shows_in_stamp_report' => true,
            ],
            [
                'name' => 'Penalty Payment',
                'name_ar' => 'دفع غرامة',
                'description' => 'Penalty payment for late submission',
                'default_value' => 50.00,
                'has_penalty' => true,
                'has_tax_file' => true,
                'shows_in_stamp_report' => false,
            ],
            [
                'name' => 'Exempt Transaction',
                'name_ar' => 'معاملة معفاة',
                'description' => 'Tax exempt transaction',
                'default_value' => 0.00,
                'is_tax_burden_exempt' => true,
                'has_tax_file' => true,
                'shows_in_stamp_report' => true,
            ],
            [
                'name' => 'Promissory Note Tax',
                'name_ar' => 'ضريبة كمبيالة',
                'description' => 'Tax on promissory notes',
                'default_value' => 1.00,
                'has_promissory_note' => true,
                'has_parties' => true,
                'has_first_party' => true,
                'has_second_party' => true,
                'has_multiple_values' => true,
                'shows_in_stamp_report' => true,
            ],
        ];

        foreach ($transactionTypes as $transactionType) {
            TransactionType::create($transactionType);
        }

        // Create admin user
        $centralDepartment = Department::where('name_ar', 'الإدارة الضريبية المركزية')->first();
        $mainOffice = $centralDepartment->offices()->where('name', 'like', '%Main Office%')->first();
        $mainTreasury = $mainOffice->treasuries()->where('name_ar', 'الخزينة الرئيسية')->first();

        $adminUser = User::create([
            'name' => 'System Administrator',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'department_id' => $centralDepartment->id,
            'office_id' => $mainOffice->id,
            'treasury_id' => $mainTreasury->id,
            'employee_id' => 'EMP001',
            'phone' => '+20-1234567890',
            'is_active' => true,
        ]);

        $adminUser->assignRole('super-admin');

        // Create department admin user
        $deptAdminUser = User::create([
            'name' => 'Department Administrator',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'department_id' => $centralDepartment->id,
            'office_id' => $mainOffice->id,
            'treasury_id' => $mainTreasury->id,
            'employee_id' => 'EMP002',
            'phone' => '+20-1234567891',
            'is_active' => true,
        ]);

        $deptAdminUser->assignRole('department-admin');
    }
}
