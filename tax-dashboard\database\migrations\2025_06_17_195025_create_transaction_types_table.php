<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('transaction_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_ar')->nullable();
            $table->text('description')->nullable();
            $table->decimal('default_value', 10, 2)->nullable();

            // Boolean flags for dynamic behavior
            $table->boolean('is_percentage')->default(false);
            $table->boolean('has_promissory_note')->default(false);
            $table->boolean('has_contract')->default(false);
            $table->boolean('has_penalty')->default(false);
            $table->boolean('is_tax_burden_exempt')->default(false);
            $table->boolean('has_tax_file')->default(false);
            $table->boolean('has_parties')->default(false);
            $table->boolean('has_first_party')->default(false);
            $table->boolean('has_second_party')->default(false);
            $table->boolean('has_third_party')->default(false);
            $table->boolean('has_start_date')->default(false);
            $table->boolean('has_end_date')->default(false);
            $table->boolean('shows_in_stamp_report')->default(false);
            $table->boolean('has_transaction_count')->default(false);
            $table->boolean('has_multiple_values')->default(false);
            $table->boolean('has_clearances')->default(false);

            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('transaction_types');
    }
};
