<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exempt_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('department_id')->constrained()->onDelete('cascade');
            $table->foreignId('office_id')->constrained()->onDelete('cascade');
            $table->foreignId('transaction_type_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('reference_number')->unique();

            // Entity information (can be company or individual)
            $table->string('entity_name');
            $table->string('entity_id')->nullable();
            $table->string('entity_type')->default('company'); // company, individual

            // Exemption details
            $table->string('exemption_reason');
            $table->text('exemption_justification')->nullable();
            $table->decimal('original_amount', 15, 2);
            $table->decimal('exempted_amount', 15, 2);

            $table->string('status')->default('draft'); // draft, approved, rejected
            $table->text('notes')->nullable();

            // Dynamic fields based on transaction type
            $table->json('dynamic_fields')->nullable();

            // Dates
            $table->date('transaction_date')->default(now());
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exempt_transactions');
    }
};
