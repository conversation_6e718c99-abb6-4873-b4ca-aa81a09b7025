<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ReceiptResource\Pages;
use App\Filament\Resources\ReceiptResource\RelationManagers;
use App\Models\Receipt;
use App\Models\Treasury;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Support\Enums\FontWeight;

class ReceiptResource extends Resource
{
    protected static ?string $model = Receipt::class;

    protected static ?string $navigationIcon = 'heroicon-o-receipt-percent';

    protected static ?string $navigationGroup = 'عمليات الخزينة';

    protected static ?string $modelLabel = 'إيصال';

    protected static ?string $pluralModelLabel = 'الإيصالات';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات الإيصال')
                    ->schema([
                        Forms\Components\TextInput::make('receipt_number')
                            ->label('رقم الإيصال')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->default(fn () => 'REC-' . now()->format('YmdHis')),
                        Forms\Components\Select::make('treasury_id')
                            ->label('الخزينة')
                            ->options(Treasury::active()->with('office.department')->get()->mapWithKeys(function ($treasury) {
                                return [$treasury->id => $treasury->office->department->name_ar . ' - ' . $treasury->office->name_ar . ' - ' . $treasury->name_ar];
                            }))
                            ->required()
                            ->searchable(),
                        Forms\Components\Select::make('user_id')
                            ->label('المستخدم')
                            ->options(User::active()->pluck('name', 'id'))
                            ->required()
                            ->default(auth()->id())
                            ->searchable(),
                        Forms\Components\TextInput::make('amount')
                            ->label('المبلغ')
                            ->required()
                            ->numeric()
                            ->step(0.01)
                            ->prefix('ج.م'),
                        Forms\Components\Select::make('payment_method')
                            ->label('طريقة الدفع')
                            ->options([
                                'cash' => 'نقدي',
                                'electronic' => 'إلكتروني',
                                'check' => 'شيك',
                            ])
                            ->required()
                            ->default('cash'),
                        Forms\Components\TextInput::make('payer_name')
                            ->label('اسم الدافع')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('payer_id')
                            ->label('رقم هوية الدافع')
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->label('الوصف')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('معلومات الإلغاء')
                    ->schema([
                        Forms\Components\Toggle::make('is_cancelled')
                            ->label('ملغي')
                            ->disabled()
                            ->dehydrated(false),
                        Forms\Components\DateTimePicker::make('cancelled_at')
                            ->label('تاريخ الإلغاء')
                            ->disabled()
                            ->dehydrated(false),
                        Forms\Components\Select::make('cancelled_by')
                            ->label('ألغي بواسطة')
                            ->options(User::pluck('name', 'id'))
                            ->disabled()
                            ->dehydrated(false),
                        Forms\Components\Textarea::make('cancellation_reason')
                            ->label('سبب الإلغاء')
                            ->rows(3)
                            ->disabled()
                            ->dehydrated(false)
                            ->columnSpanFull(),
                    ])
                    ->columns(2)
                    ->visible(fn ($record) => $record && $record->is_cancelled),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('receipt_number')
                    ->label('رقم الإيصال')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Bold),
                Tables\Columns\TextColumn::make('treasury.name_ar')
                    ->label('الخزينة')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('treasury.office.department.name_ar')
                    ->label('الإدارة')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('المستخدم')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label('المبلغ')
                    ->numeric(decimalPlaces: 2)
                    ->sortable()
                    ->prefix('ج.م ')
                    ->color(fn ($state) => $state > 0 ? 'success' : 'danger'),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label('طريقة الدفع')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'cash' => 'نقدي',
                        'electronic' => 'إلكتروني',
                        'check' => 'شيك',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'cash' => 'success',
                        'electronic' => 'info',
                        'check' => 'warning',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('payer_name')
                    ->label('اسم الدافع')
                    ->searchable()
                    ->limit(20),
                Tables\Columns\IconColumn::make('is_cancelled')
                    ->label('الحالة')
                    ->boolean()
                    ->trueIcon('heroicon-o-x-circle')
                    ->falseIcon('heroicon-o-check-circle')
                    ->trueColor('danger')
                    ->falseColor('success'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('cancelled_at')
                    ->label('تاريخ الإلغاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('treasury_id')
                    ->label('الخزينة')
                    ->options(Treasury::active()->with('office.department')->get()->mapWithKeys(function ($treasury) {
                        return [$treasury->id => $treasury->office->department->name_ar . ' - ' . $treasury->name_ar];
                    })),
                Tables\Filters\SelectFilter::make('payment_method')
                    ->label('طريقة الدفع')
                    ->options([
                        'cash' => 'نقدي',
                        'electronic' => 'إلكتروني',
                        'check' => 'شيك',
                    ]),
                Tables\Filters\TernaryFilter::make('is_cancelled')
                    ->label('الحالة')
                    ->trueLabel('ملغي')
                    ->falseLabel('نشط')
                    ->native(false),
                Tables\Filters\Filter::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('من تاريخ'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('إلى تاريخ'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => !$record->is_cancelled),
                Tables\Actions\Action::make('cancel')
                    ->label('إلغاء')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->form([
                        Forms\Components\Textarea::make('cancellation_reason')
                            ->label('سبب الإلغاء')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function ($record, array $data) {
                        $record->cancel($data['cancellation_reason'], auth()->id());
                    })
                    ->visible(fn ($record) => !$record->is_cancelled),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () => auth()->user()->can('delete_receipts')),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListReceipts::route('/'),
            'create' => Pages\CreateReceipt::route('/create'),
            'edit' => Pages\EditReceipt::route('/{record}/edit'),
        ];
    }
}
