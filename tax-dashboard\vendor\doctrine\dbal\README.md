# Doctrine DBAL

|                   [5.0-dev][5.0]                    |                   [4.3-dev][4.3]                    |                     [4.2][4.2]                      |                     [3.10][3.10]                      |                     [3.9][3.9]                      |
|:---------------------------------------------------:|:---------------------------------------------------:|:---------------------------------------------------:|:-----------------------------------------------------:|:---------------------------------------------------:|
|      [![GitHub Actions][GA 5.0 image]][GA 5.0]      |      [![GitHub Actions][GA 4.3 image]][GA 4.3]      |      [![GitHub Actions][GA 4.2 image]][GA 4.2]      |      [![GitHub Actions][GA 3.10 image]][GA 3.10]      |      [![GitHub Actions][GA 3.9 image]][GA 3.9]      |
|   [![AppVeyor][AppVeyor 5.0 image]][AppVeyor 5.0]   |   [![AppVeyor][AppVeyor 4.3 image]][AppVeyor 4.3]   |   [![AppVeyor][AppVeyor 4.2 image]][AppVeyor 4.2]   |   [![AppVeyor][AppVeyor 3.10 image]][AppVeyor 3.10]   |   [![AppVeyor][AppVeyor 3.9 image]][AppVeyor 3.9]   |
| [![Code Coverage][Coverage 5.0 image]][CodeCov 5.0] | [![Code Coverage][Coverage 4.3 image]][CodeCov 4.3] | [![Code Coverage][Coverage 4.2 image]][CodeCov 4.2] | [![Code Coverage][Coverage 3.10 image]][CodeCov 3.10] | [![Code Coverage][Coverage 3.9 image]][CodeCov 3.9] |

Powerful ***D***ata***B***ase ***A***bstraction ***L***ayer with many features for database schema introspection and schema management.

## More resources:

* [Website](http://www.doctrine-project.org/projects/dbal.html)
* [Documentation](http://docs.doctrine-project.org/projects/doctrine-dbal/en/latest/)
* [Issue Tracker](https://github.com/doctrine/dbal/issues)

  [Coverage 5.0 image]: https://codecov.io/gh/doctrine/dbal/branch/5.0.x/graph/badge.svg
  [5.0]: https://github.com/doctrine/dbal/tree/5.0.x
  [CodeCov 5.0]: https://codecov.io/gh/doctrine/dbal/branch/5.0.x
  [AppVeyor 5.0]: https://ci.appveyor.com/project/doctrine/dbal/branch/5.0.x
  [AppVeyor 5.0 image]: https://ci.appveyor.com/api/projects/status/i88kitq8qpbm0vie/branch/5.0.x?svg=true
  [GA 5.0]: https://github.com/doctrine/dbal/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A5.0.x
  [GA 5.0 image]: https://github.com/doctrine/dbal/actions/workflows/continuous-integration.yml/badge.svg?branch=5.0.x

  [Coverage 4.3 image]: https://codecov.io/gh/doctrine/dbal/branch/4.3.x/graph/badge.svg
  [4.3]: https://github.com/doctrine/dbal/tree/4.3.x
  [CodeCov 4.3]: https://codecov.io/gh/doctrine/dbal/branch/4.3.x
  [AppVeyor 4.3]: https://ci.appveyor.com/project/doctrine/dbal/branch/4.3.x
  [AppVeyor 4.3 image]: https://ci.appveyor.com/api/projects/status/i88kitq8qpbm0vie/branch/4.3.x?svg=true
  [GA 4.3]: https://github.com/doctrine/dbal/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A4.3.x
  [GA 4.3 image]: https://github.com/doctrine/dbal/actions/workflows/continuous-integration.yml/badge.svg?branch=4.3.x

  [Coverage 4.2 image]: https://codecov.io/gh/doctrine/dbal/branch/4.2.x/graph/badge.svg
  [4.2]: https://github.com/doctrine/dbal/tree/4.2.x
  [CodeCov 4.2]: https://codecov.io/gh/doctrine/dbal/branch/4.2.x
  [AppVeyor 4.2]: https://ci.appveyor.com/project/doctrine/dbal/branch/4.2.x
  [AppVeyor 4.2 image]: https://ci.appveyor.com/api/projects/status/i88kitq8qpbm0vie/branch/4.2.x?svg=true
  [GA 4.2]: https://github.com/doctrine/dbal/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A4.2.x
  [GA 4.2 image]: https://github.com/doctrine/dbal/actions/workflows/continuous-integration.yml/badge.svg?branch=4.2.x

  [Coverage 3.10 image]: https://codecov.io/gh/doctrine/dbal/branch/3.10.x/graph/badge.svg
  [3.10]: https://github.com/doctrine/dbal/tree/3.10.x
  [CodeCov 3.10]: https://codecov.io/gh/doctrine/dbal/branch/3.10.x
  [AppVeyor 3.10]: https://ci.appveyor.com/project/doctrine/dbal/branch/3.10.x
  [AppVeyor 3.10 image]: https://ci.appveyor.com/api/projects/status/i88kitq8qpbm0vie/branch/3.10.x?svg=true
  [GA 3.10]: https://github.com/doctrine/dbal/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A3.10.x
  [GA 3.10 image]: https://github.com/doctrine/dbal/actions/workflows/continuous-integration.yml/badge.svg?branch=3.10.x

  [Coverage 3.9 image]: https://codecov.io/gh/doctrine/dbal/branch/3.9.x/graph/badge.svg
  [3.9]: https://github.com/doctrine/dbal/tree/3.9.x
  [CodeCov 3.9]: https://codecov.io/gh/doctrine/dbal/branch/3.9.x
  [AppVeyor 3.9]: https://ci.appveyor.com/project/doctrine/dbal/branch/3.9.x
  [AppVeyor 3.9 image]: https://ci.appveyor.com/api/projects/status/i88kitq8qpbm0vie/branch/3.9.x?svg=true
  [GA 3.9]: https://github.com/doctrine/dbal/actions?query=workflow%3A%22Continuous+Integration%22+branch%3A3.9.x
  [GA 3.9 image]: https://github.com/doctrine/dbal/actions/workflows/continuous-integration.yml/badge.svg?branch=3.9.x
