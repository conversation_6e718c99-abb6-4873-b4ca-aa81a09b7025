<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TransactionType extends Model
{
    protected $fillable = [
        'name',
        'name_ar',
        'description',
        'default_value',
        'is_percentage',
        'has_promissory_note',
        'has_contract',
        'has_penalty',
        'is_tax_burden_exempt',
        'has_tax_file',
        'has_parties',
        'has_first_party',
        'has_second_party',
        'has_third_party',
        'has_start_date',
        'has_end_date',
        'shows_in_stamp_report',
        'has_transaction_count',
        'has_multiple_values',
        'has_clearances',
        'is_active',
    ];

    protected $casts = [
        'default_value' => 'decimal:2',
        'is_percentage' => 'boolean',
        'has_promissory_note' => 'boolean',
        'has_contract' => 'boolean',
        'has_penalty' => 'boolean',
        'is_tax_burden_exempt' => 'boolean',
        'has_tax_file' => 'boolean',
        'has_parties' => 'boolean',
        'has_first_party' => 'boolean',
        'has_second_party' => 'boolean',
        'has_third_party' => 'boolean',
        'has_start_date' => 'boolean',
        'has_end_date' => 'boolean',
        'shows_in_stamp_report' => 'boolean',
        'has_transaction_count' => 'boolean',
        'has_multiple_values' => 'boolean',
        'has_clearances' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function taxpayerTransactions(): HasMany
    {
        return $this->hasMany(TaxpayerTransaction::class);
    }

    public function individualTransactions(): HasMany
    {
        return $this->hasMany(IndividualTransaction::class);
    }

    public function exemptTransactions(): HasMany
    {
        return $this->hasMany(ExemptTransaction::class);
    }

    public function completedTransactions(): HasMany
    {
        return $this->hasMany(CompletedTransaction::class);
    }

    public function treasuryTransactions(): HasMany
    {
        return $this->hasMany(TreasuryTransaction::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForIndividuals($query)
    {
        return $query->where('has_parties', false)
                    ->orWhere('has_first_party', true);
    }

    public function scopeExemptEligible($query)
    {
        return $query->where('is_tax_burden_exempt', true);
    }

    // Helper methods
    public function getDynamicFields(): array
    {
        $fields = [];

        if ($this->has_promissory_note) {
            $fields['promissory_note'] = 'text';
        }

        if ($this->has_contract) {
            $fields['contract_details'] = 'text';
        }

        if ($this->has_penalty) {
            $fields['penalty_amount'] = 'decimal';
        }

        if ($this->has_tax_file) {
            $fields['tax_file_number'] = 'string';
        }

        if ($this->has_first_party) {
            $fields['first_party_name'] = 'string';
            $fields['first_party_id'] = 'string';
        }

        if ($this->has_second_party) {
            $fields['second_party_name'] = 'string';
            $fields['second_party_id'] = 'string';
        }

        if ($this->has_third_party) {
            $fields['third_party_name'] = 'string';
            $fields['third_party_id'] = 'string';
        }

        if ($this->has_start_date) {
            $fields['start_date'] = 'date';
        }

        if ($this->has_end_date) {
            $fields['end_date'] = 'date';
        }

        if ($this->has_transaction_count) {
            $fields['transaction_count'] = 'integer';
        }

        if ($this->has_multiple_values) {
            $fields['additional_values'] = 'array';
        }

        if ($this->has_clearances) {
            $fields['clearance_details'] = 'text';
        }

        return $fields;
    }
}
