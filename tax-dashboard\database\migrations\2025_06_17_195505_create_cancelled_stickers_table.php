<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cancelled_stickers', function (Blueprint $table) {
            $table->id();
            $table->string('sticker_number')->unique();
            $table->foreignId('department_id')->constrained()->onDelete('cascade');
            $table->foreignId('office_id')->constrained()->onDelete('cascade');
            $table->foreignId('cancelled_by')->constrained('users');

            // Original sticker information
            $table->decimal('original_value', 15, 2);
            $table->string('sticker_type')->nullable();
            $table->string('series_number')->nullable();

            // Cancellation details
            $table->string('cancellation_reason');
            $table->text('cancellation_notes')->nullable();
            $table->timestamp('cancelled_at');

            // Reference to original transaction if applicable
            $table->string('original_transaction_type')->nullable();
            $table->unsignedBigInteger('original_transaction_id')->nullable();

            $table->timestamps();

            // Index for polymorphic relationship to original transaction
            $table->index(['original_transaction_type', 'original_transaction_id'], 'cancelled_stickers_original_transaction_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cancelled_stickers');
    }
};
