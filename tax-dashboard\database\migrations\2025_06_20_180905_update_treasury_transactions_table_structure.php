<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('treasury_transactions', function (Blueprint $table) {
            // Add new columns for the updated structure
            $table->string('source_type')->after('id')->nullable();
            $table->unsignedBigInteger('source_id')->after('source_type')->nullable();
            $table->foreignId('department_id')->after('source_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('office_id')->after('department_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('entity_name')->after('user_id')->nullable();
            $table->string('entity_id')->after('entity_name')->nullable();
            $table->string('entity_type')->after('entity_id')->nullable();
            $table->decimal('total_amount', 10, 2)->after('entity_type')->default(0);
            $table->decimal('stamp_tax', 10, 2)->after('total_amount')->default(0);
            $table->decimal('penalty_amount', 10, 2)->after('stamp_tax')->default(0);
            $table->decimal('clearance_tax', 10, 2)->after('penalty_amount')->default(0);
            $table->string('receipt_number')->after('clearance_tax')->nullable();
            $table->timestamp('printed_at')->after('receipt_number')->nullable();
            $table->foreignId('printed_by')->after('printed_at')->nullable()->constrained('users')->onDelete('set null');
            $table->json('transaction_data')->after('dynamic_fields')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('treasury_transactions', function (Blueprint $table) {
            $table->dropColumn([
                'source_type', 'source_id', 'department_id', 'office_id',
                'entity_name', 'entity_id', 'entity_type', 'total_amount',
                'stamp_tax', 'penalty_amount', 'clearance_tax', 'receipt_number',
                'printed_at', 'printed_by', 'transaction_data'
            ]);
        });
    }
};
