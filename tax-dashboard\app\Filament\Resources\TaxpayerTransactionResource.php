<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TaxpayerTransactionResource\Pages;
use App\Filament\Resources\TaxpayerTransactionResource\RelationManagers;
use App\Models\TaxpayerTransaction;
use App\Models\Department;
use App\Models\Office;
use App\Models\TransactionType;
use App\Models\LegalForm;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Support\Enums\FontWeight;

class TaxpayerTransactionResource extends Resource
{
    protected static ?string $model = TaxpayerTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';

    protected static ?string $navigationGroup = 'عمليات الدمغة';

    protected static ?string $modelLabel = 'معاملة ممول';

    protected static ?string $pluralModelLabel = 'معاملات الممولين';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات المعاملة')
                    ->schema([
                        Forms\Components\TextInput::make('reference_number')
                            ->label('رقم المرجع')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->default(fn () => 'TAX-' . now()->format('YmdHis')),
                        Forms\Components\Select::make('department_id')
                            ->label('الإدارة')
                            ->options(Department::active()->pluck('name_ar', 'id'))
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(fn (callable $set) => $set('office_id', null)),
                        Forms\Components\Select::make('office_id')
                            ->label('المكتب')
                            ->options(function (callable $get) {
                                $departmentId = $get('department_id');
                                if (!$departmentId) return [];
                                return Office::where('department_id', $departmentId)->active()->pluck('name_ar', 'id');
                            })
                            ->required()
                            ->reactive(),
                        Forms\Components\Select::make('transaction_type_id')
                            ->label('نوع المعاملة')
                            ->options(TransactionType::active()->pluck('name_ar', 'id'))
                            ->required()
                            ->reactive()
                            ->searchable(),
                        Forms\Components\DatePicker::make('transaction_date')
                            ->label('تاريخ المعاملة')
                            ->required()
                            ->default(now()),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('معلومات الممول')
                    ->schema([
                        Forms\Components\TextInput::make('taxpayer_name')
                            ->label('اسم الممول')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('taxpayer_id')
                            ->label('رقم هوية الممول')
                            ->maxLength(255),
                        Forms\Components\Select::make('taxpayer_type')
                            ->label('نوع الممول')
                            ->options([
                                'company' => 'شركة',
                                'individual' => 'فرد',
                            ])
                            ->required()
                            ->default('company')
                            ->reactive(),
                        Forms\Components\Select::make('legal_form_id')
                            ->label('الشكل القانوني')
                            ->options(LegalForm::active()->pluck('name_ar', 'id'))
                            ->visible(fn (callable $get) => $get('taxpayer_type') === 'company')
                            ->searchable(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('تفاصيل المعاملة')
                    ->schema([
                        Forms\Components\TextInput::make('amount')
                            ->label('المبلغ')
                            ->required()
                            ->numeric()
                            ->step(0.01)
                            ->prefix('ج.م'),
                        Forms\Components\Select::make('status')
                            ->label('الحالة')
                            ->options([
                                'draft' => 'مسودة',
                                'submitted' => 'مقدمة',
                                'completed' => 'مكتملة',
                                'cancelled' => 'ملغية',
                            ])
                            ->required()
                            ->default('draft'),
                        Forms\Components\Textarea::make('notes')
                            ->label('ملاحظات')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                // Dynamic fields will be added here based on transaction type
                Forms\Components\Section::make('الحقول الديناميكية')
                    ->schema(function (callable $get) {
                        $transactionTypeId = $get('transaction_type_id');
                        if (!$transactionTypeId) return [];

                        $transactionType = TransactionType::find($transactionTypeId);
                        if (!$transactionType) return [];

                        $fields = [];
                        $dynamicFields = $transactionType->getDynamicFields();

                        foreach ($dynamicFields as $fieldName => $fieldType) {
                            $label = match($fieldName) {
                                'promissory_note' => 'تفاصيل الكمبيالة',
                                'contract_details' => 'تفاصيل العقد',
                                'penalty_amount' => 'مبلغ الغرامة',
                                'tax_file_number' => 'رقم الملف الضريبي',
                                'first_party_name' => 'اسم الطرف الأول',
                                'first_party_id' => 'رقم هوية الطرف الأول',
                                'second_party_name' => 'اسم الطرف الثاني',
                                'second_party_id' => 'رقم هوية الطرف الثاني',
                                'third_party_name' => 'اسم الطرف الثالث',
                                'third_party_id' => 'رقم هوية الطرف الثالث',
                                'start_date' => 'تاريخ البداية',
                                'end_date' => 'تاريخ النهاية',
                                'transaction_count' => 'عدد المعاملات',
                                'clearance_details' => 'تفاصيل المخالصة',
                                default => $fieldName,
                            };

                            $field = match($fieldType) {
                                'text' => Forms\Components\Textarea::make("dynamic_fields.{$fieldName}")
                                    ->label($label)
                                    ->rows(3),
                                'decimal' => Forms\Components\TextInput::make("dynamic_fields.{$fieldName}")
                                    ->label($label)
                                    ->numeric()
                                    ->step(0.01),
                                'integer' => Forms\Components\TextInput::make("dynamic_fields.{$fieldName}")
                                    ->label($label)
                                    ->numeric(),
                                'date' => Forms\Components\DatePicker::make("dynamic_fields.{$fieldName}")
                                    ->label($label),
                                'string' => Forms\Components\TextInput::make("dynamic_fields.{$fieldName}")
                                    ->label($label)
                                    ->maxLength(255),
                                default => Forms\Components\TextInput::make("dynamic_fields.{$fieldName}")
                                    ->label($label),
                            };

                            $fields[] = $field;
                        }

                        return $fields;
                    })
                    ->columns(2)
                    ->visible(fn (callable $get) => !empty($get('transaction_type_id'))),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('reference_number')
                    ->label('رقم المرجع')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Bold),
                Tables\Columns\TextColumn::make('taxpayer_name')
                    ->label('اسم الممول')
                    ->searchable()
                    ->sortable()
                    ->limit(30),
                Tables\Columns\TextColumn::make('transactionType.name_ar')
                    ->label('نوع المعاملة')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('department.name_ar')
                    ->label('الإدارة')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('office.name_ar')
                    ->label('المكتب')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('taxpayer_type')
                    ->label('نوع الممول')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'company' => 'شركة',
                        'individual' => 'فرد',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'company' => 'info',
                        'individual' => 'success',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('amount')
                    ->label('المبلغ')
                    ->numeric(decimalPlaces: 2)
                    ->sortable()
                    ->prefix('ج.م ')
                    ->color('success'),
                Tables\Columns\TextColumn::make('status')
                    ->label('الحالة')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'draft' => 'مسودة',
                        'submitted' => 'مقدمة',
                        'completed' => 'مكتملة',
                        'cancelled' => 'ملغية',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'draft' => 'gray',
                        'submitted' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('تاريخ المعاملة')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('المستخدم')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('department_id')
                    ->label('الإدارة')
                    ->options(Department::active()->pluck('name_ar', 'id')),
                Tables\Filters\SelectFilter::make('office_id')
                    ->label('المكتب')
                    ->options(Office::active()->with('department')->get()->mapWithKeys(function ($office) {
                        return [$office->id => $office->department->name_ar . ' - ' . $office->name_ar];
                    })),
                Tables\Filters\SelectFilter::make('transaction_type_id')
                    ->label('نوع المعاملة')
                    ->options(TransactionType::active()->pluck('name_ar', 'id')),
                Tables\Filters\SelectFilter::make('taxpayer_type')
                    ->label('نوع الممول')
                    ->options([
                        'company' => 'شركة',
                        'individual' => 'فرد',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->label('الحالة')
                    ->options([
                        'draft' => 'مسودة',
                        'submitted' => 'مقدمة',
                        'completed' => 'مكتملة',
                        'cancelled' => 'ملغية',
                    ]),
                Tables\Filters\Filter::make('transaction_date')
                    ->label('تاريخ المعاملة')
                    ->form([
                        Forms\Components\DatePicker::make('transaction_from')
                            ->label('من تاريخ'),
                        Forms\Components\DatePicker::make('transaction_until')
                            ->label('إلى تاريخ'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['transaction_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('transaction_date', '>=', $date),
                            )
                            ->when(
                                $data['transaction_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('transaction_date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => in_array($record->status, ['draft', 'submitted'])),
                Tables\Actions\Action::make('submit')
                    ->label('تقديم')
                    ->icon('heroicon-o-paper-airplane')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->action(fn ($record) => $record->submit())
                    ->visible(fn ($record) => $record->status === 'draft'),
                Tables\Actions\Action::make('complete')
                    ->label('إكمال')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->action(fn ($record) => $record->complete())
                    ->visible(fn ($record) => $record->status === 'submitted'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () => auth()->user()->can('delete_taxpayer_transactions')),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTaxpayerTransactions::route('/'),
            'create' => Pages\CreateTaxpayerTransaction::route('/create'),
            'edit' => Pages\EditTaxpayerTransaction::route('/{record}/edit'),
        ];
    }
}
