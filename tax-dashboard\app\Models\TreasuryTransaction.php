<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TreasuryTransaction extends Model
{
    protected $fillable = [
        'source_type',
        'source_id',
        'department_id',
        'office_id',
        'treasury_id',
        'transaction_type_id',
        'user_id',
        'entity_name',
        'entity_id',
        'entity_type',
        'total_amount',
        'stamp_tax',
        'penalty_amount',
        'clearance_tax',
        'status',
        'receipt_number',
        'printed_at',
        'printed_by',
        'notes',
        'transaction_data',
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'stamp_tax' => 'decimal:2',
        'penalty_amount' => 'decimal:2',
        'clearance_tax' => 'decimal:2',
        'transaction_data' => 'array',
        'printed_at' => 'datetime',
    ];

    // Relationships
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function office(): BelongsTo
    {
        return $this->belongsTo(Office::class);
    }

    public function treasury(): BelongsTo
    {
        return $this->belongsTo(Treasury::class);
    }

    public function transactionType(): BelongsTo
    {
        return $this->belongsTo(TransactionType::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function printedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'printed_by');
    }

    // Scopes
    public function scopePendingPayment($query)
    {
        return $query->where('status', 'pending_payment');
    }

    public function scopeReceiptPrinted($query)
    {
        return $query->where('status', 'receipt_printed');
    }

    // Helper methods
    public function printReceipt($userId): bool
    {
        $this->update([
            'status' => 'receipt_printed',
            'receipt_number' => 'REC-' . now()->format('YmdHis') . '-' . $this->id,
            'printed_at' => now(),
            'printed_by' => $userId,
        ]);

        return true;
    }

    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'pending_payment' => 'في انتظار الدفع',
            'receipt_printed' => 'تم طباعة الإيصال',
            'completed' => 'مكتملة',
            default => $this->status,
        };
    }
}
