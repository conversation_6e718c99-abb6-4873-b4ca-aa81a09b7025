<?php

namespace App\Filament\Resources\ExemptTransactionResource\Pages;

use App\Filament\Resources\ExemptTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditExemptTransaction extends EditRecord
{
    protected static string $resource = ExemptTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
