<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Department extends Model
{
    protected $fillable = [
        'name',
        'name_ar',
        'is_active',
        'description',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function offices(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Office::class);
    }

    public function users(): Has<PERSON><PERSON>
    {
        return $this->hasMany(User::class);
    }

    public function treasuries(): HasMany
    {
        return $this->hasMany(Treasury::class);
    }

    public function taxpayerTransactions(): HasMany
    {
        return $this->hasMany(TaxpayerTransaction::class);
    }

    public function individualTransactions(): Has<PERSON><PERSON>
    {
        return $this->hasMany(IndividualTransaction::class);
    }

    public function exemptTransactions(): Has<PERSON>any
    {
        return $this->hasMany(ExemptTransaction::class);
    }

    public function completedTransactions(): HasMany
    {
        return $this->hasMany(CompletedTransaction::class);
    }

    public function cancelledStickers(): HasMany
    {
        return $this->hasMany(CancelledSticker::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
