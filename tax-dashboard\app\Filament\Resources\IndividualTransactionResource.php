<?php

namespace App\Filament\Resources;

use App\Filament\Resources\IndividualTransactionResource\Pages;
use App\Filament\Resources\IndividualTransactionResource\RelationManagers;
use App\Models\IndividualTransaction;
use App\Models\Department;
use App\Models\Office;
use App\Models\TransactionType;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Support\Enums\FontWeight;

class IndividualTransactionResource extends Resource
{
    protected static ?string $model = IndividualTransaction::class;

    // protected static ?string $navigationIcon = 'heroicon-o-user';

    protected static ?string $navigationGroup = 'عمليات الدمغة';

    protected static ?string $modelLabel = 'معاملة فرد';

    protected static ?string $pluralModelLabel = 'معاملات الأفراد';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('البيانات الأساسية')
                    ->schema([
                        Forms\Components\Select::make('department_id')
                            ->label('الإدارة')
                            ->options(Department::active()->pluck('name_ar', 'id'))
                            ->default(fn () => auth()->user()->department_id ?? 1)
                            ->disabled()
                            ->dehydrated(),
                        Forms\Components\Select::make('office_id')
                            ->label('المكتب')
                            ->options(function () {
                                $departmentId = auth()->user()->department_id ?? 1;
                                return Office::where('department_id', $departmentId)->active()->pluck('name_ar', 'id');
                            })
                            ->default(fn () => auth()->user()->office_id ?? 1)
                            ->disabled()
                            ->dehydrated(),
                        Forms\Components\Select::make('transaction_type_id')
                            ->label('نوع المعاملة')
                            ->options(TransactionType::active()->where(function($query) {
                                // Only show transaction types suitable for individuals
                                $query->where('has_tax_file', false)
                                      ->where('is_tax_burden_exempt', false);
                            })->pluck('name_ar', 'id'))
                            ->required()
                            ->reactive()
                            ->searchable(),
                        Forms\Components\DatePicker::make('transaction_date')
                            ->label('تاريخ المعاملة')
                            ->required()
                            ->default(now()),
                        Forms\Components\Placeholder::make('calculation_info')
                            ->label('آلية حساب الضريبة')
                            ->content(function (callable $get) {
                                $transactionTypeId = $get('transaction_type_id');
                                if (!$transactionTypeId) return 'اختر نوع المعاملة لعرض آلية الحساب';

                                $transactionType = TransactionType::find($transactionTypeId);
                                if (!$transactionType) return '';

                                $info = [];

                                if ($transactionType->is_percentage) {
                                    $info[] = "💰 نسبة مئوية: {$transactionType->default_value}% من قيمة المعاملة";
                                } else {
                                    $info[] = "💰 قيمة ثابتة: {$transactionType->default_value} جنيه";
                                }

                                if ($transactionType->has_transaction_count) {
                                    $info[] = "🔢 يتم الضرب في عدد المعاملات";
                                }

                                if ($transactionType->has_multiple_values) {
                                    $info[] = "📊 يتم جمع القيم المتعددة";
                                }

                                return implode("\n", $info);
                            })
                            ->columnSpanFull(),
                        Forms\Components\Hidden::make('reference_number')
                            ->default(fn () => 'IND-' . now()->format('YmdHis')),
                        Forms\Components\Hidden::make('user_id')
                            ->default(auth()->id()),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('معلومات الفرد')
                    ->schema([
                        Forms\Components\TextInput::make('individual_name')
                            ->label('اسم الفرد')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('national_id')
                            ->label('رقم الهوية')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('phone')
                            ->label('رقم الهاتف')
                            ->tel()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('address')
                            ->label('العنوان')
                            ->rows(2)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                // Hidden fields for automatic handling
                Forms\Components\Hidden::make('amount')
                    ->default(0),
                Forms\Components\Hidden::make('status')
                    ->default('pending_payment'),
                Forms\Components\Hidden::make('dynamic_fields')
                    ->default('{}'),
                Forms\Components\Hidden::make('submitted_at'),
                Forms\Components\Hidden::make('completed_at'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('reference_number')
                    ->label('رقم المرجع')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Bold),
                Tables\Columns\TextColumn::make('individual_name')
                    ->label('اسم الفرد')
                    ->searchable()
                    ->sortable()
                    ->limit(30),
                Tables\Columns\TextColumn::make('national_id')
                    ->label('رقم الهوية')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('transactionType.name_ar')
                    ->label('نوع المعاملة')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('department.name_ar')
                    ->label('الإدارة')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('office.name_ar')
                    ->label('المكتب')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label('المبلغ')
                    ->numeric(decimalPlaces: 2)
                    ->sortable()
                    ->prefix('ج.م ')
                    ->color('success'),
                Tables\Columns\TextColumn::make('status')
                    ->label('الحالة')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'draft' => 'مسودة',
                        'submitted' => 'مقدمة',
                        'completed' => 'مكتملة',
                        'cancelled' => 'ملغية',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'draft' => 'gray',
                        'submitted' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('تاريخ المعاملة')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label('الهاتف')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('المستخدم')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('department_id')
                    ->label('الإدارة')
                    ->options(Department::active()->pluck('name_ar', 'id')),
                Tables\Filters\SelectFilter::make('office_id')
                    ->label('المكتب')
                    ->options(Office::active()->with('department')->get()->mapWithKeys(function ($office) {
                        return [$office->id => $office->department->name_ar . ' - ' . $office->name_ar];
                    })),
                Tables\Filters\SelectFilter::make('transaction_type_id')
                    ->label('نوع المعاملة')
                    ->options(TransactionType::active()->where('has_tax_file', false)->pluck('name_ar', 'id')),
                Tables\Filters\SelectFilter::make('status')
                    ->label('الحالة')
                    ->options([
                        'draft' => 'مسودة',
                        'submitted' => 'مقدمة',
                        'completed' => 'مكتملة',
                        'cancelled' => 'ملغية',
                    ]),
                Tables\Filters\Filter::make('transaction_date')
                    ->label('تاريخ المعاملة')
                    ->form([
                        Forms\Components\DatePicker::make('transaction_from')
                            ->label('من تاريخ'),
                        Forms\Components\DatePicker::make('transaction_until')
                            ->label('إلى تاريخ'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['transaction_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('transaction_date', '>=', $date),
                            )
                            ->when(
                                $data['transaction_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('transaction_date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => in_array($record->status, ['draft', 'submitted'])),
                Tables\Actions\Action::make('submit')
                    ->label('تقديم')
                    ->icon('heroicon-o-paper-airplane')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $record->update([
                            'status' => 'submitted',
                            'submitted_at' => now(),
                        ]);
                    })
                    ->visible(fn ($record) => $record->status === 'draft'),
                Tables\Actions\Action::make('complete')
                    ->label('إكمال')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $record->update([
                            'status' => 'completed',
                            'completed_at' => now(),
                        ]);
                    })
                    ->visible(fn ($record) => $record->status === 'submitted'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () => auth()->user()->can('delete_individual_transactions')),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->modifyQueryUsing(function (Builder $query) {
                // Default filter: show only today's transactions
                return $query->whereDate('transaction_date', today());
            });
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListIndividualTransactions::route('/'),
            'create' => Pages\CreateIndividualTransaction::route('/create'),
            'edit' => Pages\EditIndividualTransaction::route('/{record}/edit'),
        ];
    }
}
