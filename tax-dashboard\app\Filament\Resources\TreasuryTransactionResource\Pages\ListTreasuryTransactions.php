<?php

namespace App\Filament\Resources\TreasuryTransactionResource\Pages;

use App\Filament\Resources\TreasuryTransactionResource;
use App\Models\Department;
use App\Models\Office;
use App\Models\Treasury;
use Filament\Actions;
use Filament\Forms;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListTreasuryTransactions extends ListRecords
{
    protected static string $resource = TreasuryTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('treasury_report')
                ->label('تقرير الخزينة')
                ->icon('heroicon-o-chart-bar')
                ->color('success')
                ->form([
                    Forms\Components\DatePicker::make('from_date')
                        ->label('من تاريخ')
                        ->required()
                        ->default(today()),
                    Forms\Components\DatePicker::make('to_date')
                        ->label('إلى تاريخ')
                        ->required()
                        ->default(today()),
                    Forms\Components\Select::make('department_id')
                        ->label('الإدارة')
                        ->options(Department::active()->pluck('name_ar', 'id'))
                        ->reactive()
                        ->afterStateUpdated(fn (callable $set) => $set('office_id', null)),
                    Forms\Components\Select::make('office_id')
                        ->label('المكتب')
                        ->options(function (callable $get) {
                            $departmentId = $get('department_id');
                            if (!$departmentId) return [];
                            return Office::where('department_id', $departmentId)->active()->pluck('name_ar', 'id');
                        })
                        ->reactive()
                        ->afterStateUpdated(fn (callable $set) => $set('treasury_id', null)),
                    Forms\Components\Select::make('treasury_id')
                        ->label('الخزينة')
                        ->options(function (callable $get) {
                            $officeId = $get('office_id');
                            if (!$officeId) return [];
                            return Treasury::where('office_id', $officeId)->active()->pluck('name_ar', 'id');
                        }),
                ])
                ->action(function (array $data) {
                    $this->notify('success', 'سيتم تطوير تقرير الخزينة قريباً');
                })
                ->modalWidth('md'),
            Actions\Action::make('stamp_report')
                ->label('تقرير الدمغة')
                ->icon('heroicon-o-document-text')
                ->color('warning')
                ->action(function () {
                    $this->notify('success', 'سيتم تطوير تقرير الدمغة قريباً');
                }),
            Actions\Action::make('bonds_report')
                ->label('تقرير الصكوك')
                ->icon('heroicon-o-document-check')
                ->color('info')
                ->action(function () {
                    $this->notify('success', 'سيتم تطوير تقرير الصكوك قريباً');
                }),
        ];
    }

    public function getTabs(): array
    {
        return [
            'pending_payment' => Tab::make('في انتظار الدفع')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'pending_payment'))
                ->badge(fn () => $this->getModel()::where('status', 'pending_payment')->whereDate('created_at', today())->count()),
            'paid' => Tab::make('تم الدفع')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'paid'))
                ->badge(fn () => $this->getModel()::where('status', 'paid')->whereDate('created_at', today())->count()),
            'receipt_issued' => Tab::make('تم إصدار إيصال')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'receipt_printed'))
                ->badge(fn () => $this->getModel()::where('status', 'receipt_printed')->whereDate('created_at', today())->count()),
            'sticker_issued' => Tab::make('تم إصدار ملصق')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'sticker_printed'))
                ->badge(fn () => $this->getModel()::where('status', 'sticker_printed')->whereDate('created_at', today())->count()),
        ];
    }
}
