<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('electronic_payments', function (Blueprint $table) {
            $table->id();
            $table->string('transaction_id')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('treasury_id')->constrained()->onDelete('cascade');

            // Payment details
            $table->decimal('amount', 15, 2);
            $table->string('payment_method'); // credit_card, bank_transfer, mobile_payment, etc.
            $table->string('payment_gateway')->nullable(); // visa, mastercard, fawry, etc.

            // Payer information
            $table->string('payer_name');
            $table->string('payer_email')->nullable();
            $table->string('payer_phone')->nullable();

            // Transaction status
            $table->string('status')->default('pending'); // pending, completed, failed, refunded
            $table->string('gateway_transaction_id')->nullable();
            $table->text('gateway_response')->nullable();

            // Timestamps
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamp('failed_at')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('electronic_payments');
    }
};
