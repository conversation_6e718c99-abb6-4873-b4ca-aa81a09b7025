<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pre_printed_receipt_series', function (Blueprint $table) {
            $table->id();
            $table->string('series_name');
            $table->foreignId('treasury_id')->constrained()->onDelete('cascade');
            $table->foreignId('office_id')->constrained()->onDelete('cascade');
            $table->foreignId('department_id')->constrained()->onDelete('cascade');
            $table->foreignId('assigned_user_id')->nullable()->constrained('users')->onDelete('set null');

            // Series details
            $table->string('prefix')->nullable(); // e.g., "TAX-"
            $table->unsignedInteger('start_number');
            $table->unsignedInteger('end_number');
            $table->unsignedInteger('current_number');

            // Status
            $table->boolean('is_active')->default(true);
            $table->boolean('is_exhausted')->default(false);
            $table->timestamp('exhausted_at')->nullable();

            // Metadata
            $table->text('description')->nullable();
            $table->date('valid_from')->default(now());
            $table->date('valid_until')->nullable();

            $table->timestamps();

            // Ensure current number is within range
            $table->index(['treasury_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pre_printed_receipt_series');
    }
};
