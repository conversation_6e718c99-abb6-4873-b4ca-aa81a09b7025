<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('completed_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('source_type'); // taxpayer_transactions, individual_transactions, etc.
            $table->unsignedBigInteger('source_id');
            $table->foreignId('department_id')->constrained()->onDelete('cascade');
            $table->foreignId('office_id')->constrained()->onDelete('cascade');
            $table->foreignId('transaction_type_id')->constrained()->onDelete('cascade');
            $table->foreignId('completed_by')->constrained('users');

            // Entity information
            $table->string('entity_name');
            $table->string('entity_id')->nullable();
            $table->string('entity_type'); // company, individual

            // Completion details
            $table->decimal('total_amount', 15, 2);
            $table->decimal('paid_amount', 15, 2);
            $table->string('payment_method')->default('cash');
            $table->string('receipt_number')->nullable();

            $table->text('completion_notes')->nullable();
            $table->json('completion_data')->nullable(); // Store completion-specific data

            $table->timestamp('completed_at');
            $table->timestamps();

            // Index for polymorphic relationship
            $table->index(['source_type', 'source_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('completed_transactions');
    }
};
