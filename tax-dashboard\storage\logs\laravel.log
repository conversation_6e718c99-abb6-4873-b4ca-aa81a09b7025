[2025-06-17 19:47:52] local.ERROR: could not find driver (Connection: sqlite, SQL: select exists(select * from "users" where "email" = <EMAIL>) as "exists") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: sqlite, SQL: select exists(select * from \"users\" where \"email\" = <EMAIL>) as \"exists\") at F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists(s...', Array, Object(Closure))
#1 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists(s...', Array, Object(Closure))
#2 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3504): Illuminate\\Database\\Connection->select('select exists(s...', Array, true)
#3 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->exists()
#4 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\filament\\filament\\src\\Commands\\MakeUserCommand.php(48): Illuminate\\Database\\Eloquent\\Builder->__call('exists', Array)
#5 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\ConfiguresPrompts.php(138): Filament\\Commands\\MakeUserCommand->Filament\\Commands\\{closure}('<EMAIL>')
#6 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\ConfiguresPrompts.php(38): Illuminate\\Console\\Command->promptUntilValid(Object(Closure), true, Object(Closure))
#7 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\prompts\\src\\Concerns\\Fallback.php(59): Illuminate\\Console\\Command->Illuminate\\Console\\Concerns\\{closure}(Object(Laravel\\Prompts\\TextPrompt))
#8 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\prompts\\src\\Prompt.php(108): Laravel\\Prompts\\Prompt->fallback()
#9 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\prompts\\src\\helpers.php(14): Laravel\\Prompts\\Prompt->prompt()
#10 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\filament\\filament\\src\\Commands\\MakeUserCommand.php(43): Laravel\\Prompts\\text('Email address', '', '', true, Object(Closure))
#11 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\filament\\filament\\src\\Commands\\MakeUserCommand.php(62): Filament\\Commands\\MakeUserCommand->getUserData()
#12 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\filament\\filament\\src\\Commands\\MakeUserCommand.php(100): Filament\\Commands\\MakeUserCommand->createUser()
#13 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Commands\\MakeUserCommand->handle()
#14 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#15 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#18 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#19 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#21 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Filament\\Commands\\MakeUserCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#27 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('sqlite:F:\\\\0____...', NULL, Object(SensitiveParameterValue), Array)
#1 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:F:\\\\0____...', NULL, Object(SensitiveParameterValue), Array)
#2 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(21): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:F:\\\\0____...', Array, Array)
#3 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(223): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#6 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists(s...', Array)
#10 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists(s...', Array, Object(Closure))
#11 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists(s...', Array, Object(Closure))
#12 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3504): Illuminate\\Database\\Connection->select('select exists(s...', Array, true)
#13 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->exists()
#14 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\filament\\filament\\src\\Commands\\MakeUserCommand.php(48): Illuminate\\Database\\Eloquent\\Builder->__call('exists', Array)
#15 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\ConfiguresPrompts.php(138): Filament\\Commands\\MakeUserCommand->Filament\\Commands\\{closure}('<EMAIL>')
#16 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\ConfiguresPrompts.php(38): Illuminate\\Console\\Command->promptUntilValid(Object(Closure), true, Object(Closure))
#17 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\prompts\\src\\Concerns\\Fallback.php(59): Illuminate\\Console\\Command->Illuminate\\Console\\Concerns\\{closure}(Object(Laravel\\Prompts\\TextPrompt))
#18 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\prompts\\src\\Prompt.php(108): Laravel\\Prompts\\Prompt->fallback()
#19 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\prompts\\src\\helpers.php(14): Laravel\\Prompts\\Prompt->prompt()
#20 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\filament\\filament\\src\\Commands\\MakeUserCommand.php(43): Laravel\\Prompts\\text('Email address', '', '', true, Object(Closure))
#21 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\filament\\filament\\src\\Commands\\MakeUserCommand.php(62): Filament\\Commands\\MakeUserCommand->getUserData()
#22 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\filament\\filament\\src\\Commands\\MakeUserCommand.php(100): Filament\\Commands\\MakeUserCommand->createUser()
#23 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Filament\\Commands\\MakeUserCommand->handle()
#24 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#29 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#31 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Filament\\Commands\\MakeUserCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#37 {main}
"} 
[2025-06-17 19:48:00] local.ERROR: could not find driver (Connection: sqlite, SQL: select exists (select 1 from "main".sqlite_master where name = 'migrations' and type = 'table') as "exists") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: sqlite, SQL: select exists (select 1 from \"main\".sqlite_master where name = 'migrations' and type = 'table') as \"exists\") at F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#2 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#3 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#4 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#5 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#9 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#10 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#11 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#12 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#13 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#14 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#15 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#16 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#21 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#22 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#29 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('sqlite:F:\\\\0____...', NULL, Object(SensitiveParameterValue), Array)
#1 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:F:\\\\0____...', NULL, Object(SensitiveParameterValue), Array)
#2 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(21): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:F:\\\\0____...', Array, Array)
#3 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(223): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#6 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#10 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#11 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#12 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#13 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#14 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#15 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#16 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#17 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#18 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#19 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#20 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#21 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#22 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#23 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#24 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#25 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#26 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#31 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#39 {main}
"} 
[2025-06-17 19:59:06] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1059 Identifier name 'cancelled_stickers_original_transaction_type_original_transaction_id_index' is too long (Connection: mysql, SQL: alter table `cancelled_stickers` add index `cancelled_stickers_original_transaction_type_original_transaction_id_index`(`original_transaction_type`, `original_transaction_id`)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1059 Identifier name 'cancelled_stickers_original_transaction_type_original_transaction_id_index' is too long (Connection: mysql, SQL: alter table `cancelled_stickers` add index `cancelled_stickers_original_transaction_type_original_transaction_id_index`(`original_transaction_type`, `original_transaction_id`)) at F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `ca...', Array, Object(Closure))
#1 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `ca...', Array, Object(Closure))
#2 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `ca...')
#3 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('cancelled_stick...', Object(Closure))
#6 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\database\\migrations\\2025_06_17_195505_create_cancelled_stickers_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_17_1955...', Object(Closure))
#13 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_17_1955...', Object(Closure))
#14 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('F:\\\\0_____source...', 2, false)
#15 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1059 Identifier name 'cancelled_stickers_original_transaction_type_original_transaction_id_index' is too long at F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `ca...', Array)
#2 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `ca...', Array, Object(Closure))
#3 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `ca...', Array, Object(Closure))
#4 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `ca...')
#5 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('cancelled_stick...', Object(Closure))
#8 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\database\\migrations\\2025_06_17_195505_create_cancelled_stickers_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_17_1955...', Object(Closure))
#15 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_17_1955...', Object(Closure))
#16 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('F:\\\\0_____source...', 2, false)
#17 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-17 19:59:29] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'cancelled_stickers' already exists (Connection: mysql, SQL: create table `cancelled_stickers` (`id` bigint unsigned not null auto_increment primary key, `sticker_number` varchar(255) not null, `department_id` bigint unsigned not null, `office_id` bigint unsigned not null, `cancelled_by` bigint unsigned not null, `original_value` decimal(15, 2) not null, `sticker_type` varchar(255) null, `series_number` varchar(255) null, `cancellation_reason` varchar(255) not null, `cancellation_notes` text null, `cancelled_at` timestamp not null, `original_transaction_type` varchar(255) null, `original_transaction_id` bigint unsigned null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'cancelled_stickers' already exists (Connection: mysql, SQL: create table `cancelled_stickers` (`id` bigint unsigned not null auto_increment primary key, `sticker_number` varchar(255) not null, `department_id` bigint unsigned not null, `office_id` bigint unsigned not null, `cancelled_by` bigint unsigned not null, `original_value` decimal(15, 2) not null, `sticker_type` varchar(255) null, `series_number` varchar(255) null, `cancellation_reason` varchar(255) not null, `cancellation_notes` text null, `cancelled_at` timestamp not null, `original_transaction_type` varchar(255) null, `original_transaction_id` bigint unsigned null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `c...', Array, Object(Closure))
#1 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `c...', Array, Object(Closure))
#2 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `c...')
#3 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('cancelled_stick...', Object(Closure))
#6 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\database\\migrations\\2025_06_17_195505_create_cancelled_stickers_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_17_1955...', Object(Closure))
#13 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_17_1955...', Object(Closure))
#14 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('F:\\\\0_____source...', 3, false)
#15 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'cancelled_stickers' already exists at F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `c...', Array)
#2 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `c...', Array, Object(Closure))
#3 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `c...', Array, Object(Closure))
#4 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `c...')
#5 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('cancelled_stick...', Object(Closure))
#8 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\database\\migrations\\2025_06_17_195505_create_cancelled_stickers_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_17_1955...', Object(Closure))
#15 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_17_1955...', Object(Closure))
#16 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('F:\\\\0_____source...', 3, false)
#17 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-17 20:24:41] local.ERROR: Call to a member function treasuries() on null {"exception":"[object] (Error(code: 0): Call to a member function treasuries() on null at F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\database\\seeders\\BasicDataSeeder.php:180)
[stacktrace]
#0 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\BasicDataSeeder->run()
#1 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#2 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#3 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#4 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#5 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#6 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#7 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#8 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#9 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#10 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#11 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#12 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#13 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#14 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#15 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#16 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#18 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#24 {main}
"} 
[2025-06-17 20:29:28] local.ERROR: A `view_departments` permission already exists for guard `web`. {"exception":"[object] (Spatie\\Permission\\Exceptions\\PermissionAlreadyExists(code: 0): A `view_departments` permission already exists for guard `web`. at F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\PermissionAlreadyExists.php:11)
[stacktrace]
#0 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php(49): Spatie\\Permission\\Exceptions\\PermissionAlreadyExists::create('view_department...', 'web')
#1 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\database\\seeders\\RolePermissionSeeder.php(134): Spatie\\Permission\\Models\\Permission::create(Array)
#2 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\RolePermissionSeeder->run()
#3 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#7 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#8 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#9 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#10 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#11 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#12 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#13 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#18 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\0_____sources\\0_____tax\\01\\tax-dashboard\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#26 {main}
"} 
