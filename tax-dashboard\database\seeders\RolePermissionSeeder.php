<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // Data Management
            'view_departments',
            'create_departments',
            'edit_departments',
            'delete_departments',

            'view_offices',
            'create_offices',
            'edit_offices',
            'delete_offices',

            'view_tax_types',
            'create_tax_types',
            'edit_tax_types',
            'delete_tax_types',

            'view_activity_types',
            'create_activity_types',
            'edit_activity_types',
            'delete_activity_types',

            'view_legal_forms',
            'create_legal_forms',
            'edit_legal_forms',
            'delete_legal_forms',

            'view_transaction_types',
            'create_transaction_types',
            'edit_transaction_types',
            'delete_transaction_types',

            // Treasury Operations
            'view_treasuries',
            'create_treasuries',
            'edit_treasuries',
            'delete_treasuries',

            'view_receipts',
            'create_receipts',
            'edit_receipts',
            'delete_receipts',
            'cancel_receipts',

            'view_treasury_transactions',
            'create_treasury_transactions',
            'edit_treasury_transactions',
            'delete_treasury_transactions',
            'complete_treasury_transactions',

            'view_electronic_payments',
            'process_electronic_payments',

            'view_receipt_series',
            'create_receipt_series',
            'edit_receipt_series',
            'delete_receipt_series',

            // Stamp Operations
            'view_taxpayer_transactions',
            'create_taxpayer_transactions',
            'edit_taxpayer_transactions',
            'delete_taxpayer_transactions',
            'submit_taxpayer_transactions',
            'complete_taxpayer_transactions',

            'view_individual_transactions',
            'create_individual_transactions',
            'edit_individual_transactions',
            'delete_individual_transactions',
            'submit_individual_transactions',
            'complete_individual_transactions',

            'view_exempt_transactions',
            'create_exempt_transactions',
            'edit_exempt_transactions',
            'delete_exempt_transactions',
            'approve_exempt_transactions',

            'view_completed_transactions',

            'view_cancelled_stickers',
            'create_cancelled_stickers',

            // User Management
            'view_users',
            'create_users',
            'edit_users',
            'delete_users',
            'assign_roles',

            'view_roles',
            'create_roles',
            'edit_roles',
            'delete_roles',

            'view_permissions',
            'assign_permissions',

            // System Settings
            'view_audit_logs',
            'view_error_logs',
            'manage_system_settings',
            'manage_email_settings',

            // Reports
            'view_reports',
            'export_reports',
            'view_stamp_reports',
            'view_treasury_reports',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Super Admin - has all permissions
        $superAdmin = Role::create(['name' => 'super-admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Department Admin - can manage their department
        $departmentAdmin = Role::create(['name' => 'department-admin']);
        $departmentAdmin->givePermissionTo([
            'view_offices', 'create_offices', 'edit_offices', 'delete_offices',
            'view_treasuries', 'create_treasuries', 'edit_treasuries', 'delete_treasuries',
            'view_users', 'create_users', 'edit_users', 'assign_roles',
            'view_receipts', 'create_receipts', 'edit_receipts', 'cancel_receipts',
            'view_treasury_transactions', 'create_treasury_transactions', 'edit_treasury_transactions', 'complete_treasury_transactions',
            'view_taxpayer_transactions', 'create_taxpayer_transactions', 'edit_taxpayer_transactions', 'submit_taxpayer_transactions', 'complete_taxpayer_transactions',
            'view_individual_transactions', 'create_individual_transactions', 'edit_individual_transactions', 'submit_individual_transactions', 'complete_individual_transactions',
            'view_exempt_transactions', 'create_exempt_transactions', 'edit_exempt_transactions', 'approve_exempt_transactions',
            'view_completed_transactions',
            'view_cancelled_stickers', 'create_cancelled_stickers',
            'view_receipt_series', 'create_receipt_series', 'edit_receipt_series',
            'view_reports', 'export_reports', 'view_stamp_reports', 'view_treasury_reports',
        ]);

        // Office Manager - can manage their office
        $officeManager = Role::create(['name' => 'office-manager']);
        $officeManager->givePermissionTo([
            'view_treasuries', 'edit_treasuries',
            'view_users', 'edit_users',
            'view_receipts', 'create_receipts', 'edit_receipts',
            'view_treasury_transactions', 'create_treasury_transactions', 'edit_treasury_transactions', 'complete_treasury_transactions',
            'view_taxpayer_transactions', 'create_taxpayer_transactions', 'edit_taxpayer_transactions', 'submit_taxpayer_transactions',
            'view_individual_transactions', 'create_individual_transactions', 'edit_individual_transactions', 'submit_individual_transactions',
            'view_exempt_transactions', 'create_exempt_transactions', 'edit_exempt_transactions',
            'view_completed_transactions',
            'view_cancelled_stickers', 'create_cancelled_stickers',
            'view_receipt_series',
            'view_reports', 'view_stamp_reports', 'view_treasury_reports',
        ]);

        // Treasury Officer - handles treasury operations
        $treasuryOfficer = Role::create(['name' => 'treasury-officer']);
        $treasuryOfficer->givePermissionTo([
            'view_receipts', 'create_receipts', 'edit_receipts',
            'view_treasury_transactions', 'create_treasury_transactions', 'edit_treasury_transactions', 'complete_treasury_transactions',
            'view_electronic_payments', 'process_electronic_payments',
            'view_receipt_series',
            'view_treasury_reports',
        ]);

        // Transaction Officer - handles transaction processing
        $transactionOfficer = Role::create(['name' => 'transaction-officer']);
        $transactionOfficer->givePermissionTo([
            'view_taxpayer_transactions', 'create_taxpayer_transactions', 'edit_taxpayer_transactions', 'submit_taxpayer_transactions',
            'view_individual_transactions', 'create_individual_transactions', 'edit_individual_transactions', 'submit_individual_transactions',
            'view_exempt_transactions', 'create_exempt_transactions', 'edit_exempt_transactions',
            'view_completed_transactions',
            'view_cancelled_stickers', 'create_cancelled_stickers',
            'view_stamp_reports',
        ]);

        // Data Entry Clerk - basic data entry
        $dataEntryClerk = Role::create(['name' => 'data-entry-clerk']);
        $dataEntryClerk->givePermissionTo([
            'view_taxpayer_transactions', 'create_taxpayer_transactions', 'edit_taxpayer_transactions',
            'view_individual_transactions', 'create_individual_transactions', 'edit_individual_transactions',
            'view_exempt_transactions', 'create_exempt_transactions',
            'view_completed_transactions',
        ]);

        // Viewer - read-only access
        $viewer = Role::create(['name' => 'viewer']);
        $viewer->givePermissionTo([
            'view_departments', 'view_offices', 'view_treasuries',
            'view_receipts', 'view_treasury_transactions',
            'view_taxpayer_transactions', 'view_individual_transactions',
            'view_exempt_transactions', 'view_completed_transactions',
            'view_cancelled_stickers', 'view_receipt_series',
            'view_reports', 'view_stamp_reports', 'view_treasury_reports',
        ]);
    }
}
