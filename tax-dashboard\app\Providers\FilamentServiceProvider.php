<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Filament\Facades\Filament;
use Filament\Navigation\NavigationGroup;

class FilamentServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Filament::serving(function () {
            // Set navigation groups with Arabic labels
            Filament::registerNavigationGroups([
                NavigationGroup::make('إدارة البيانات')
                    ->icon('heroicon-o-circle-stack')
                    ->collapsible(),
                NavigationGroup::make('عمليات الخزينة')
                    ->icon('heroicon-o-currency-dollar')
                    ->collapsible(),
                NavigationGroup::make('عمليات الدمغة')
                    ->icon('heroicon-o-document-text')
                    ->collapsible(),
                NavigationGroup::make('الإعدادات العامة')
                    ->icon('heroicon-o-cog-6-tooth')
                    ->collapsible(),
                NavigationGroup::make('التقارير')
                    ->icon('heroicon-o-chart-bar')
                    ->collapsible(),
            ]);
        });
    }
}
