<?php

namespace App\Filament\Resources\IndividualTransactionResource\Pages;

use App\Filament\Resources\IndividualTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditIndividualTransaction extends EditRecord
{
    protected static string $resource = IndividualTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
