<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    public function run(): void
    {
        $settings = [
            [
                'key' => 'site_name',
                'value' => 'نظام إدارة الضرائب',
                'type' => 'string',
                'group' => 'general',
                'label' => 'اسم الموقع',
                'description' => 'اسم الموقع الذي يظهر في الواجهة',
            ],
            [
                'key' => 'logo_height',
                'value' => '50',
                'type' => 'integer',
                'group' => 'general',
                'label' => 'ارتفاع الشعار',
                'description' => 'ارتفاع الشعار بالبكسل',
            ],
            [
                'key' => 'primary_color',
                'value' => '#059669',
                'type' => 'color',
                'group' => 'appearance',
                'label' => 'اللون الأساسي',
                'description' => 'اللون الأساسي للموقع',
            ],
            [
                'key' => 'secondary_color',
                'value' => '#6B7280',
                'type' => 'color',
                'group' => 'appearance',
                'label' => 'اللون الثانوي',
                'description' => 'اللون الثانوي للموقع',
            ],
            [
                'key' => 'success_color',
                'value' => '#10B981',
                'type' => 'color',
                'group' => 'appearance',
                'label' => 'لون النجاح',
                'description' => 'لون النجاح في الواجهة',
            ],
            [
                'key' => 'warning_color',
                'value' => '#F59E0B',
                'type' => 'color',
                'group' => 'appearance',
                'label' => 'لون التحذير',
                'description' => 'لون التحذير في الواجهة',
            ],
            [
                'key' => 'danger_color',
                'value' => '#EF4444',
                'type' => 'color',
                'group' => 'appearance',
                'label' => 'لون الخطر',
                'description' => 'لون الخطر في الواجهة',
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
