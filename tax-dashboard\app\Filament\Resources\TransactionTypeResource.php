<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TransactionTypeResource\Pages;
use App\Filament\Resources\TransactionTypeResource\RelationManagers;
use App\Models\TransactionType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TransactionTypeResource extends Resource
{
    protected static ?string $model = TransactionType::class;

    // protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'إدارة البيانات';

    protected static ?string $modelLabel = 'نوع معاملة';

    protected static ?string $pluralModelLabel = 'أنواع المعاملات';

    protected static ?int $navigationSort = 6;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات أساسية')
                    ->schema([
                        Forms\Components\TextInput::make('name_ar')
                            ->label('اسم نوع المعاملة')
                            ->required()
                            ->maxLength(255)
                            ->afterStateUpdated(fn ($state, callable $set) => $set('name', $state)),
                        Forms\Components\Hidden::make('name'),
                        Forms\Components\Textarea::make('description')
                            ->label('الوصف')
                            ->rows(3)
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('default_value')
                            ->label('القيمة الافتراضية')
                            ->numeric()
                            ->step(0.01),
                        Forms\Components\Toggle::make('is_percentage')
                            ->label('نسبة مئوية')
                            ->default(false),
                        Forms\Components\Toggle::make('is_active')
                            ->label('مفعل')
                            ->default(true),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('إعدادات الحقول الديناميكية')
                    ->schema([
                        Forms\Components\Toggle::make('has_promissory_note')
                            ->label('يحتوي على كمبيالة')
                            ->default(false),
                        Forms\Components\Toggle::make('has_contract')
                            ->label('يحتوي على عقد')
                            ->default(false),
                        Forms\Components\Toggle::make('has_penalty')
                            ->label('يحتوي على غرامة')
                            ->default(false),
                        Forms\Components\Toggle::make('is_tax_burden_exempt')
                            ->label('معفي من العبء الضريبي')
                            ->default(false),
                        Forms\Components\Toggle::make('has_tax_file')
                            ->label('يحتوي على ملف ضريبي')
                            ->default(false),
                        Forms\Components\Toggle::make('has_parties')
                            ->label('يحتوي على أطراف')
                            ->default(false),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('إعدادات الأطراف')
                    ->schema([
                        Forms\Components\Toggle::make('has_first_party')
                            ->label('طرف أول')
                            ->default(false),
                        Forms\Components\Toggle::make('has_second_party')
                            ->label('طرف ثاني')
                            ->default(false),
                        Forms\Components\Toggle::make('has_third_party')
                            ->label('طرف ثالث')
                            ->default(false),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('إعدادات التواريخ والتقارير')
                    ->schema([
                        Forms\Components\Toggle::make('has_start_date')
                            ->label('تاريخ بداية')
                            ->default(false),
                        Forms\Components\Toggle::make('has_end_date')
                            ->label('تاريخ نهاية')
                            ->default(false),
                        Forms\Components\Toggle::make('shows_in_stamp_report')
                            ->label('يظهر في تقرير الدمغة')
                            ->default(true),
                        Forms\Components\Toggle::make('has_transaction_count')
                            ->label('عدد المعاملات')
                            ->default(false),
                        Forms\Components\Toggle::make('has_multiple_values')
                            ->label('قيم متعددة')
                            ->default(false),
                        Forms\Components\Toggle::make('has_clearances')
                            ->label('مخالصات')
                            ->default(false),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')
                    ->label('اسم نوع المعاملة')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('default_value')
                    ->label('القيمة الافتراضية')
                    ->numeric(decimalPlaces: 2)
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_percentage')
                    ->label('نسبة مئوية')
                    ->boolean()
                    ->trueColor('success')
                    ->falseColor('gray'),
                Tables\Columns\IconColumn::make('is_tax_burden_exempt')
                    ->label('معفي من العبء الضريبي')
                    ->boolean()
                    ->trueColor('warning')
                    ->falseColor('gray'),
                Tables\Columns\IconColumn::make('shows_in_stamp_report')
                    ->label('تقرير الدمغة')
                    ->boolean()
                    ->trueColor('info')
                    ->falseColor('gray'),
                Tables\Columns\TextColumn::make('dynamic_fields_summary')
                    ->label('الحقول الديناميكية')
                    ->getStateUsing(function ($record) {
                        $fields = [];
                        if ($record->has_promissory_note) $fields[] = 'كمبيالة';
                        if ($record->has_contract) $fields[] = 'عقد';
                        if ($record->has_penalty) $fields[] = 'غرامة';
                        if ($record->has_tax_file) $fields[] = 'ملف ضريبي';
                        if ($record->has_parties) $fields[] = 'أطراف';
                        return implode(', ', $fields) ?: 'لا يوجد';
                    })
                    ->wrap(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('الحالة')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('الحالة')
                    ->trueLabel('مفعل')
                    ->falseLabel('غير مفعل')
                    ->native(false),
                Tables\Filters\TernaryFilter::make('is_tax_burden_exempt')
                    ->label('معفي من العبء الضريبي')
                    ->trueLabel('معفي')
                    ->falseLabel('غير معفي')
                    ->native(false),
                Tables\Filters\TernaryFilter::make('shows_in_stamp_report')
                    ->label('يظهر في تقرير الدمغة')
                    ->trueLabel('نعم')
                    ->falseLabel('لا')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransactionTypes::route('/'),
            'create' => Pages\CreateTransactionType::route('/create'),
            'edit' => Pages\EditTransactionType::route('/{record}/edit'),
        ];
    }
}
