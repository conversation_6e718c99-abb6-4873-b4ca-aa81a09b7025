<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TransactionType extends Model
{
    protected $fillable = [
        'name',
        'name_ar',
        'description',
        'default_value',
        'is_percentage',
        'has_promissory_note',
        'has_contract',
        'has_penalty',
        'is_tax_burden_exempt',
        'has_tax_file',
        'has_parties',
        'has_first_party',
        'has_second_party',
        'has_third_party',
        'has_start_date',
        'has_end_date',
        'shows_in_stamp_report',
        'has_transaction_count',
        'has_multiple_values',
        'has_clearances',
        'is_active',
    ];

    protected $casts = [
        'default_value' => 'decimal:2',
        'is_percentage' => 'boolean',
        'has_promissory_note' => 'boolean',
        'has_contract' => 'boolean',
        'has_penalty' => 'boolean',
        'is_tax_burden_exempt' => 'boolean',
        'has_tax_file' => 'boolean',
        'has_parties' => 'boolean',
        'has_first_party' => 'boolean',
        'has_second_party' => 'boolean',
        'has_third_party' => 'boolean',
        'has_start_date' => 'boolean',
        'has_end_date' => 'boolean',
        'shows_in_stamp_report' => 'boolean',
        'has_transaction_count' => 'boolean',
        'has_multiple_values' => 'boolean',
        'has_clearances' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function taxpayerTransactions(): HasMany
    {
        return $this->hasMany(TaxpayerTransaction::class);
    }

    public function individualTransactions(): HasMany
    {
        return $this->hasMany(IndividualTransaction::class);
    }

    public function exemptTransactions(): HasMany
    {
        return $this->hasMany(ExemptTransaction::class);
    }

    public function completedTransactions(): HasMany
    {
        return $this->hasMany(CompletedTransaction::class);
    }

    public function treasuryTransactions(): HasMany
    {
        return $this->hasMany(TreasuryTransaction::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForIndividuals($query)
    {
        return $query->where('has_parties', false)
                    ->orWhere('has_first_party', true);
    }

    public function scopeExemptEligible($query)
    {
        return $query->where('is_tax_burden_exempt', true);
    }

    // Helper methods
    public function getDynamicFields(): array
    {
        $fields = [];

        // Contract details - shows contract number field
        if ($this->has_contract) {
            $fields['contract_number'] = [
                'type' => 'string',
                'label' => 'رقم العقد',
                'required' => true
            ];
        }

        // Promissory note details
        if ($this->has_promissory_note) {
            $fields['promissory_note_details'] = [
                'type' => 'text',
                'label' => 'تفاصيل الكمبيالة',
                'required' => false
            ];
        }

        // Tax file number - only for companies with tax files
        if ($this->has_tax_file) {
            $fields['tax_file_number'] = [
                'type' => 'string',
                'label' => 'رقم الملف الضريبي',
                'required' => true
            ];
        }

        // Party selection fields - dropdown from taxpayers database
        if ($this->has_first_party) {
            $fields['first_party_taxpayer_id'] = [
                'type' => 'select_taxpayer',
                'label' => 'الطرف الأول',
                'required' => false
            ];
        }

        if ($this->has_second_party) {
            $fields['second_party_taxpayer_id'] = [
                'type' => 'select_taxpayer',
                'label' => 'الطرف الثاني',
                'required' => false
            ];
        }

        if ($this->has_third_party) {
            $fields['third_party_taxpayer_id'] = [
                'type' => 'select_taxpayer',
                'label' => 'الطرف الثالث',
                'required' => false
            ];
        }

        // Date fields
        if ($this->has_start_date) {
            $fields['start_date'] = [
                'type' => 'date',
                'label' => 'تاريخ البداية',
                'required' => $this->has_penalty // Required if penalty calculation is enabled
            ];
        }

        if ($this->has_end_date) {
            $fields['end_date'] = [
                'type' => 'date',
                'label' => 'تاريخ النهاية',
                'required' => false
            ];
        }

        // Transaction count - for multiple transactions of same type
        if ($this->has_transaction_count) {
            $fields['transaction_count'] = [
                'type' => 'integer',
                'label' => 'عدد المعاملات',
                'required' => true,
                'default' => 1,
                'min' => 1
            ];
        }

        // Multiple values - for invoices or sub-transactions
        if ($this->has_multiple_values) {
            $fields['multiple_values'] = [
                'type' => 'repeater',
                'label' => 'القيم المتعددة',
                'required' => false,
                'depends_on' => 'transaction_count' // Number of fields depends on transaction count
            ];
        }

        // Penalty calculation - auto-calculated based on start date
        if ($this->has_penalty) {
            $fields['penalty_amount'] = [
                'type' => 'calculated',
                'label' => 'مبلغ الغرامة',
                'calculation' => 'penalty_based_on_delay',
                'readonly' => true
            ];
        }

        // Clearance details
        if ($this->has_clearances) {
            $fields['clearance_details'] = [
                'type' => 'text',
                'label' => 'تفاصيل المخالصة',
                'required' => false
            ];
        }

        return $fields;
    }

    // Calculate tax amount based on transaction type settings
    public function calculateTaxAmount($baseAmount, $transactionCount = 1, $multipleValues = []): float
    {
        // For fixed value transactions (like secondary budget)
        if (!$this->is_percentage && $this->default_value > 0) {
            // Ignore input amount, use fixed value
            $taxAmount = $this->default_value;

            // Multiply by transaction count if applicable
            if ($this->has_transaction_count) {
                $taxAmount *= $transactionCount;
            }

            return $taxAmount;
        }

        // For percentage-based calculations
        if ($this->is_percentage) {
            $calculationBase = $baseAmount;

            // If multiple values are provided, sum them up
            if ($this->has_multiple_values && !empty($multipleValues)) {
                $calculationBase = array_sum($multipleValues);
            }

            $taxAmount = $calculationBase * ($this->default_value / 100);

            // Multiply by transaction count if applicable
            if ($this->has_transaction_count) {
                $taxAmount *= $transactionCount;
            }

            return $taxAmount;
        }

        // Default case - use base amount as is
        return $baseAmount;
    }

    // Calculate penalty based on delay from start date
    public function calculatePenalty($startDate, $currentDate = null): float
    {
        if (!$this->has_penalty || !$startDate) {
            return 0;
        }

        $currentDate = $currentDate ?: now();
        $startDate = \Carbon\Carbon::parse($startDate);
        $currentDate = \Carbon\Carbon::parse($currentDate);

        // Calculate days of delay
        $delayDays = $currentDate->diffInDays($startDate, false);

        if ($delayDays <= 0) {
            return 0; // No penalty if not delayed
        }

        // Example penalty calculation: 1% per month of delay
        $penaltyRate = 0.01; // 1% per month
        $delayMonths = ceil($delayDays / 30);

        return $this->default_value * $penaltyRate * $delayMonths;
    }

    // Check if this transaction type should appear in specific contexts
    public function isVisibleForTaxpayers(): bool
    {
        // Don't show tax-burden-exempt transactions in regular taxpayer interface
        return !$this->is_tax_burden_exempt;
    }

    public function isVisibleForIndividuals(): bool
    {
        // Don't show transactions that require tax files for individuals
        return !$this->has_tax_file && !$this->is_tax_burden_exempt;
    }

    public function isVisibleForExemptTransactions(): bool
    {
        // Only show tax-burden-exempt transactions in exempt interface
        return $this->is_tax_burden_exempt;
    }
}
