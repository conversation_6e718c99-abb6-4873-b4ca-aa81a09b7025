<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TreasuryTransactionResource\Pages;
use App\Filament\Resources\TreasuryTransactionResource\RelationManagers;
use App\Models\TreasuryTransaction;
use App\Models\Treasury;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Support\Enums\FontWeight;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TreasuryTransactionResource extends Resource
{
    protected static ?string $model = TreasuryTransaction::class;

    // protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationGroup = 'عمليات الخزينة';

    protected static ?string $modelLabel = 'معاملة خزينة';

    protected static ?string $pluralModelLabel = 'معاملات الخزينة';

    protected static ?int $navigationSort = 1;

    // Disable manual creation - transactions are created automatically
    public static function canCreate(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // This form is mainly for viewing, not creating
                Forms\Components\Placeholder::make('info')
                    ->label('معلومات')
                    ->content('معاملات الخزينة يتم إنشاؤها تلقائياً من المعاملات الأخرى'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('entity_name')
                    ->label('اسم الكيان')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Bold),
                Tables\Columns\TextColumn::make('entity_type')
                    ->label('نوع الكيان')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'company' => 'شركة',
                        'individual' => 'فرد',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'company' => 'info',
                        'individual' => 'success',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('transactionType.name_ar')
                    ->label('نوع المعاملة')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('total_amount')
                    ->label('إجمالي المبلغ')
                    ->numeric(decimalPlaces: 2)
                    ->sortable()
                    ->prefix('ج.م ')
                    ->color('success'),
                Tables\Columns\TextColumn::make('status')
                    ->label('الحالة')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'pending_payment' => 'في انتظار الدفع',
                        'receipt_printed' => 'تم طباعة الإيصال',
                        'completed' => 'مكتملة',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'pending_payment' => 'warning',
                        'receipt_printed' => 'success',
                        'completed' => 'info',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('treasury.name_ar')
                    ->label('الخزينة')
                    ->sortable(),
                Tables\Columns\TextColumn::make('receipt_number')
                    ->label('رقم الإيصال')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('الحالة')
                    ->options([
                        'pending_payment' => 'في انتظار الدفع',
                        'receipt_printed' => 'تم طباعة الإيصال',
                        'completed' => 'مكتملة',
                    ])
                    ->default('pending_payment'),
                Tables\Filters\SelectFilter::make('treasury_id')
                    ->label('الخزينة')
                    ->options(Treasury::active()->with('office.department')->get()->mapWithKeys(function ($treasury) {
                        return [$treasury->id => $treasury->office->department->name_ar . ' - ' . $treasury->name_ar];
                    })),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\Action::make('print_receipt')
                    ->label('طباعة الإيصال')
                    ->icon('heroicon-o-printer')
                    ->color('success')
                    ->action(function ($record) {
                        $record->printReceipt(auth()->id());
                        // Here you would redirect to print page
                        return redirect()->back()->with('success', 'تم تحديث حالة المعاملة - جاري تحضير الطباعة');
                    })
                    ->visible(fn ($record) => $record->status === 'pending_payment'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () => auth()->user()->can('delete_treasury_transactions')),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->modifyQueryUsing(function (Builder $query) {
                // Default filter: show pending payment transactions
                return $query->where('status', 'pending_payment');
            });
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTreasuryTransactions::route('/'),
            'create' => Pages\CreateTreasuryTransaction::route('/create'),
            'edit' => Pages\EditTreasuryTransaction::route('/{record}/edit'),
        ];
    }
}
