<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    public function run(): void
    {
        $settings = [
            // إعدادات عامة
            [
                'key' => 'site_name',
                'value' => 'نظام إدارة الضرائب',
                'type' => 'string',
                'group' => 'general',
                'category' => 'general',
                'sort_order' => 1,
                'label' => 'اسم الموقع',
                'description' => 'اسم الموقع الذي يظهر في الواجهة',
            ],
            [
                'key' => 'logo_height',
                'value' => '50',
                'type' => 'integer',
                'group' => 'general',
                'category' => 'general',
                'sort_order' => 2,
                'label' => 'ارتفاع الشعار',
                'description' => 'ارتفاع الشعار بالبكسل',
            ],

            // إعدادات المظهر
            [
                'key' => 'primary_color',
                'value' => '#059669',
                'type' => 'color',
                'group' => 'appearance',
                'category' => 'appearance',
                'sort_order' => 1,
                'label' => 'اللون الأساسي',
                'description' => 'اللون الأساسي للموقع',
            ],
            [
                'key' => 'secondary_color',
                'value' => '#6B7280',
                'type' => 'color',
                'group' => 'appearance',
                'category' => 'appearance',
                'sort_order' => 2,
                'label' => 'اللون الثانوي',
                'description' => 'اللون الثانوي للموقع',
            ],
            [
                'key' => 'success_color',
                'value' => '#10B981',
                'type' => 'color',
                'group' => 'appearance',
                'category' => 'appearance',
                'sort_order' => 3,
                'label' => 'لون النجاح',
                'description' => 'لون النجاح في الواجهة',
            ],
            [
                'key' => 'warning_color',
                'value' => '#F59E0B',
                'type' => 'color',
                'group' => 'appearance',
                'category' => 'appearance',
                'sort_order' => 4,
                'label' => 'لون التحذير',
                'description' => 'لون التحذير في الواجهة',
            ],
            [
                'key' => 'danger_color',
                'value' => '#EF4444',
                'type' => 'color',
                'group' => 'appearance',
                'category' => 'appearance',
                'sort_order' => 5,
                'label' => 'لون الخطر',
                'description' => 'لون الخطر في الواجهة',
            ],
            [
                'key' => 'info_color',
                'value' => '#3B82F6',
                'type' => 'color',
                'group' => 'appearance',
                'category' => 'appearance',
                'sort_order' => 6,
                'label' => 'لون المعلومات',
                'description' => 'لون المعلومات في الواجهة',
            ],

            // إعدادات أمنية
            [
                'key' => 'force_https',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'security',
                'category' => 'security',
                'sort_order' => 1,
                'label' => 'فرض HTTPS',
                'description' => 'فرض استخدام HTTPS في جميع الصفحات',
            ],
            [
                'key' => 'session_timeout',
                'value' => '120',
                'type' => 'integer',
                'group' => 'security',
                'category' => 'security',
                'sort_order' => 2,
                'label' => 'انتهاء الجلسة',
                'description' => 'مدة انتهاء الجلسة بالدقائق',
            ],
            [
                'key' => 'max_login_attempts',
                'value' => '5',
                'type' => 'integer',
                'group' => 'security',
                'category' => 'security',
                'sort_order' => 3,
                'label' => 'محاولات تسجيل الدخول',
                'description' => 'عدد محاولات تسجيل الدخول المسموحة',
            ],
            [
                'key' => 'enable_two_factor',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'security',
                'category' => 'security',
                'sort_order' => 4,
                'label' => 'المصادقة الثنائية',
                'description' => 'تفعيل المصادقة الثنائية',
            ],

            // إعدادات النظام
            [
                'key' => 'default_language',
                'value' => 'ar',
                'type' => 'string',
                'group' => 'system',
                'category' => 'system',
                'sort_order' => 1,
                'label' => 'اللغة الافتراضية',
                'description' => 'اللغة الافتراضية للنظام',
            ],
            [
                'key' => 'timezone',
                'value' => 'Africa/Cairo',
                'type' => 'string',
                'group' => 'system',
                'category' => 'system',
                'sort_order' => 2,
                'label' => 'المنطقة الزمنية',
                'description' => 'المنطقة الزمنية للنظام',
            ],
            [
                'key' => 'items_per_page',
                'value' => '25',
                'type' => 'integer',
                'group' => 'system',
                'category' => 'system',
                'sort_order' => 3,
                'label' => 'عناصر الصفحة',
                'description' => 'عدد العناصر في الصفحة الواحدة',
            ],
            [
                'key' => 'maintenance_mode',
                'value' => 'false',
                'type' => 'boolean',
                'group' => 'system',
                'category' => 'system',
                'sort_order' => 4,
                'label' => 'وضع الصيانة',
                'description' => 'تفعيل وضع الصيانة',
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
