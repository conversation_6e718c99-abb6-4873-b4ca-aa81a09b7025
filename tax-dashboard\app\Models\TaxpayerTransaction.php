<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TaxpayerTransaction extends Model
{
    protected $fillable = [
        'department_id',
        'office_id',
        'transaction_type_id',
        'user_id',
        'reference_number',
        'taxpayer_name',
        'taxpayer_id',
        'taxpayer_type',
        'legal_form_id',
        'amount',
        'status',
        'notes',
        'dynamic_fields',
        'transaction_date',
        'submitted_at',
        'completed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'dynamic_fields' => 'array',
        'transaction_date' => 'date',
        'submitted_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    // Relationships
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function office(): BelongsTo
    {
        return $this->belongsTo(Office::class);
    }

    public function transactionType(): BelongsTo
    {
        return $this->belongsTo(TransactionType::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function legalForm(): BelongsTo
    {
        return $this->belongsTo(LegalForm::class);
    }

    // Scopes
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeSubmitted($query)
    {
        return $query->where('status', 'submitted');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeForDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    public function scopeForOffice($query, $officeId)
    {
        return $query->where('office_id', $officeId);
    }

    // Helper methods
    public function submit(): bool
    {
        $this->update([
            'status' => 'submitted',
            'submitted_at' => now(),
        ]);

        return true;
    }

    public function complete(): bool
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);

        // Create completed transaction record
        CompletedTransaction::create([
            'source_type' => self::class,
            'source_id' => $this->id,
            'department_id' => $this->department_id,
            'office_id' => $this->office_id,
            'transaction_type_id' => $this->transaction_type_id,
            'completed_by' => auth()->id(),
            'entity_name' => $this->taxpayer_name,
            'entity_id' => $this->taxpayer_id,
            'entity_type' => $this->taxpayer_type,
            'total_amount' => $this->amount,
            'paid_amount' => $this->amount,
            'payment_method' => 'cash',
            'completion_notes' => 'تم استيفاء المعاملة',
            'completion_data' => $this->dynamic_fields,
            'completed_at' => now(),
        ]);

        return true;
    }

    public function getStatusLabelAttribute(): string
    {
        return match($this->status) {
            'draft' => 'مسودة',
            'submitted' => 'مقدمة',
            'completed' => 'مكتملة',
            'cancelled' => 'ملغية',
            default => $this->status,
        };
    }

    public function getTaxpayerTypeLabelAttribute(): string
    {
        return match($this->taxpayer_type) {
            'company' => 'شركة',
            'individual' => 'فرد',
            default => $this->taxpayer_type,
        };
    }
}
