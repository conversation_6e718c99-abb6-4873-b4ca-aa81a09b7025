<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CompletedTransactionResource\Pages;
use App\Filament\Resources\CompletedTransactionResource\RelationManagers;
use App\Models\CompletedTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CompletedTransactionResource extends Resource
{
    protected static ?string $model = CompletedTransaction::class;

    // protected static ?string $navigationIcon = 'heroicon-o-check-circle';

    protected static ?string $navigationGroup = 'عمليات الدمغة';

    protected static ?string $modelLabel = 'معاملة مستوفية';

    protected static ?string $pluralModelLabel = 'المعاملات المستوفية';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('source_type')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('source_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('department_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('office_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('transaction_type_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('completed_by')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('entity_name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('entity_id')
                    ->maxLength(255),
                Forms\Components\TextInput::make('entity_type')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('total_amount')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('paid_amount')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('payment_method')
                    ->required()
                    ->maxLength(255)
                    ->default('cash'),
                Forms\Components\TextInput::make('receipt_number')
                    ->maxLength(255),
                Forms\Components\Textarea::make('completion_notes')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('completion_data'),
                Forms\Components\DateTimePicker::make('completed_at')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('source_type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('source_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('department_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('office_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('transaction_type_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('completed_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('entity_name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('entity_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('entity_type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('total_amount')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('paid_amount')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->searchable(),
                Tables\Columns\TextColumn::make('receipt_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('completed_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompletedTransactions::route('/'),
            'create' => Pages\CreateCompletedTransaction::route('/create'),
            'edit' => Pages\EditCompletedTransaction::route('/{record}/edit'),
        ];
    }
}
