<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Setting extends Model
{
    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'category',
        'sort_order',
        'label',
        'description',
    ];

    protected $casts = [
        'value' => 'string',
    ];

    // Helper methods
    public static function get($key, $default = null)
    {
        $setting = static::where('key', $key)->first();
        return $setting ? $setting->value : $default;
    }

    public static function set($key, $value)
    {
        return static::updateOrCreate(
            ['key' => $key],
            ['value' => $value]
        );
    }

    public static function getByGroup($group)
    {
        return static::where('group', $group)->get()->pluck('value', 'key');
    }

    public static function getByCategory($category)
    {
        return static::where('category', $category)->orderBy('sort_order')->get();
    }

    public static function getCategories()
    {
        return static::distinct()->pluck('category')->toArray();
    }

    public function getFormattedValueAttribute()
    {
        return match($this->type) {
            'boolean' => (bool) $this->value,
            'integer' => (int) $this->value,
            'color' => $this->value,
            'file' => $this->value,
            default => $this->value,
        };
    }
}
