<?php

namespace App\Filament\Resources\ReceiptResource\Pages;

use App\Filament\Resources\ReceiptResource;
use App\Models\Department;
use App\Models\Office;
use App\Models\Treasury;
use Filament\Actions;
use Filament\Forms;
use Filament\Resources\Pages\ListRecords;

class ListReceipts extends ListRecords
{
    protected static string $resource = ReceiptResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('treasury_report')
                ->label('تقرير الخزينة')
                ->icon('heroicon-o-chart-bar')
                ->color('success')
                ->form([
                    Forms\Components\DatePicker::make('from_date')
                        ->label('من تاريخ')
                        ->required()
                        ->default(today()),
                    Forms\Components\DatePicker::make('to_date')
                        ->label('إلى تاريخ')
                        ->required()
                        ->default(today()),
                    Forms\Components\Select::make('department_id')
                        ->label('الإدارة')
                        ->options(Department::active()->pluck('name_ar', 'id'))
                        ->reactive()
                        ->afterStateUpdated(fn (callable $set) => $set('office_id', null)),
                    Forms\Components\Select::make('office_id')
                        ->label('المكتب')
                        ->options(function (callable $get) {
                            $departmentId = $get('department_id');
                            if (!$departmentId) return [];
                            return Office::where('department_id', $departmentId)->active()->pluck('name_ar', 'id');
                        })
                        ->reactive()
                        ->afterStateUpdated(fn (callable $set) => $set('treasury_id', null)),
                    Forms\Components\Select::make('treasury_id')
                        ->label('الخزينة')
                        ->options(function (callable $get) {
                            $officeId = $get('office_id');
                            if (!$officeId) return [];
                            return Treasury::where('office_id', $officeId)->active()->pluck('name_ar', 'id');
                        }),
                ])
                ->action(function (array $data) {
                    // Generate treasury report
                    $this->notify('success', 'سيتم تطوير تقرير الخزينة قريباً');
                })
                ->modalWidth('md'),
        ];
    }
}
