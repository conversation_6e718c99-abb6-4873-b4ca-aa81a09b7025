<?php

declare(strict_types=1);

namespace Doctrine\DBAL\Platforms\Keywords;

/**
 * Oracle Keywordlist.
 */
class OracleKeywords extends KeywordList
{
    /**
     * {@inheritDoc}
     */
    protected function getKeywords(): array
    {
        return [
            'ACCESS',
            'ADD',
            'ALL',
            'ALTER',
            'AND',
            'ANY',
            'ARRAYLE<PERSON>',
            'AS',
            'ASC',
            'AUDIT',
            'BETWEEN',
            'BY',
            'CHAR',
            'CHECK',
            'CLUSTER',
            'COLUMN',
            'COMMENT',
            'COMPRESS',
            'CONNECT',
            'CREATE',
            'CURRENT',
            'DATE',
            'DECIMAL',
            'DEFAULT',
            'DELETE',
            'DESC',
            'DISTINCT',
            'DROP',
            'ELSE',
            'EXCLUSIVE',
            'EXISTS',
            'FILE',
            'FLOAT',
            'FOR',
            'FROM',
            'GRANT',
            'GROUP',
            'HAVING',
            'IDENTIFIED',
            'IMMEDIATE',
            'IN',
            'INCREMENT',
            'INDEX',
            'INITIAL',
            'INSERT',
            'INTEGER',
            'INTERSECT',
            'INTO',
            'IS',
            'LEVEL',
            'LIKE',
            'LOCK',
            'LONG',
            'MAXEXTENTS',
            'MINUS',
            'MODE',
            'MODIFY',
            'NOAUDIT',
            'NOCOMPRESS',
            'NOT',
            'NOTFOUND',
            'NOWAIT',
            'NULL',
            'NUMBER',
            'OF',
            'OFFLINE',
            'ON',
            'ONLINE',
            'OPTION',
            'OR',
            'ORDER',
            'PCTFREE',
            'PRIOR',
            'PRIVILEGES',
            'PUBLIC',
            'RANGE',
            'RAW',
            'RENAME',
            'RESOURCE',
            'REVOKE',
            'ROW',
            'ROWID',
            'ROWLABEL',
            'ROWNUM',
            'ROWS',
            'SELECT',
            'SESSION',
            'SET',
            'SHARE',
            'SIZE',
            'SMALLINT',
            'SQLBUF',
            'START',
            'SUCCESSFUL',
            'SYNONYM',
            'SYSDATE',
            'TABLE',
            'THEN',
            'TO',
            'TRIGGER',
            'UID',
            'UNION',
            'UNIQUE',
            'UPDATE',
            'USER',
            'VALIDATE',
            'VALUES',
            'VARCHAR',
            'VARCHAR2',
            'VIEW',
            'WHENEVER',
            'WHERE',
            'WITH',
        ];
    }
}
