<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'department_id',
        'office_id',
        'treasury_id',
        'employee_id',
        'phone',
        'is_active',
        'last_login_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'last_login_at' => 'datetime',
        ];
    }

    // Relationships
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function office(): BelongsTo
    {
        return $this->belongsTo(Office::class);
    }

    public function treasury(): BelongsTo
    {
        return $this->belongsTo(Treasury::class);
    }

    public function receipts(): HasMany
    {
        return $this->hasMany(Receipt::class);
    }

    public function treasuryTransactions(): HasMany
    {
        return $this->hasMany(TreasuryTransaction::class);
    }

    public function taxpayerTransactions(): HasMany
    {
        return $this->hasMany(TaxpayerTransaction::class);
    }

    public function individualTransactions(): HasMany
    {
        return $this->hasMany(IndividualTransaction::class);
    }

    public function exemptTransactions(): HasMany
    {
        return $this->hasMany(ExemptTransaction::class);
    }

    public function completedTransactions(): HasMany
    {
        return $this->hasMany(CompletedTransaction::class, 'completed_by');
    }

    public function cancelledStickers(): HasMany
    {
        return $this->hasMany(CancelledSticker::class, 'cancelled_by');
    }

    public function electronicPayments(): HasMany
    {
        return $this->hasMany(ElectronicPayment::class);
    }

    public function assignedReceiptSeries(): HasMany
    {
        return $this->hasMany(PrePrintedReceiptSeries::class, 'assigned_user_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    public function scopeForOffice($query, $officeId)
    {
        return $query->where('office_id', $officeId);
    }

    // Helper methods
    public function canAccessDepartment($departmentId): bool
    {
        // Super admin can access all departments
        if ($this->hasRole('super-admin')) {
            return true;
        }

        // Users can only access their own department
        return $this->department_id == $departmentId;
    }

    public function canAccessOffice($officeId): bool
    {
        // Super admin can access all offices
        if ($this->hasRole('super-admin')) {
            return true;
        }

        // Department admin can access all offices in their department
        if ($this->hasRole('department-admin')) {
            $office = Office::find($officeId);
            return $office && $office->department_id == $this->department_id;
        }

        // Users can only access their own office
        return $this->office_id == $officeId;
    }

    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }
}
