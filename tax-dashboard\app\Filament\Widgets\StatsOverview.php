<?php

namespace App\Filament\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use App\Models\Department;
use App\Models\Office;
use App\Models\Treasury;
use App\Models\Receipt;
use App\Models\TaxpayerTransaction;
use App\Models\Taxpayer;
use App\Models\TransactionType;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Stat::make('إجمالي الإدارات', Department::count())
                ->description('عدد الإدارات الضريبية')
                ->descriptionIcon('heroicon-m-building-office')
                ->color('success'),

            Stat::make('إجمالي المكاتب', Office::count())
                ->description('عدد المكاتب التابعة')
                ->descriptionIcon('heroicon-m-building-storefront')
                ->color('info'),

            Stat::make('إجمالي الخزائن', Treasury::count())
                ->description('عدد الخزائن المفعلة')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('warning'),

            Stat::make('إجمالي الإيصالات', Receipt::count())
                ->description('عدد الإيصالات المصدرة')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('primary'),

            Stat::make('معاملات الممولين', TaxpayerTransaction::count())
                ->description('إجمالي معاملات الممولين')
                ->descriptionIcon('heroicon-m-building-office')
                ->color('success'),

            Stat::make('إجمالي الممولين', Taxpayer::count())
                ->description('عدد الممولين المسجلين')
                ->descriptionIcon('heroicon-m-users')
                ->color('info'),

            Stat::make('أنواع المعاملات', TransactionType::count())
                ->description('عدد أنواع المعاملات')
                ->descriptionIcon('heroicon-m-clipboard-document-list')
                ->color('warning'),

            Stat::make('رصيد الخزائن', 'ج.م ' . number_format(Treasury::sum('current_balance'), 2))
                ->description('إجمالي أرصدة الخزائن')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),
        ];
    }
}
