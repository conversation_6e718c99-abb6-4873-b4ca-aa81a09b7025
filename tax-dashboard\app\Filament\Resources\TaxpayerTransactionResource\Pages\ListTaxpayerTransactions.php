<?php

namespace App\Filament\Resources\TaxpayerTransactionResource\Pages;

use App\Filament\Resources\TaxpayerTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTaxpayerTransactions extends ListRecords
{
    protected static string $resource = TaxpayerTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
