<?php

namespace App\Filament\Pages;

use App\Models\Setting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Notifications\Notification;

class ManageSettings extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    
    protected static ?string $navigationGroup = 'إدارة النظام';
    
    protected static ?string $title = 'الإعدادات العامة';
    
    protected static ?string $navigationLabel = 'الإعدادات العامة';
    
    protected static ?int $navigationSort = 3;

    protected static string $view = 'filament.pages.manage-settings';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill($this->getSettingsData());
    }

    protected function getSettingsData(): array
    {
        $settings = Setting::all();
        $data = [];
        
        foreach ($settings as $setting) {
            $data[$setting->key] = $setting->value;
        }
        
        return $data;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Tabs::make('إعدادات النظام')
                    ->tabs([
                        Forms\Components\Tabs\Tab::make('عام')
                            ->schema([
                                Forms\Components\Section::make('معلومات الموقع')
                                    ->schema([
                                        Forms\Components\TextInput::make('site_name')
                                            ->label('اسم الموقع')
                                            ->required()
                                            ->maxLength(255)
                                            ->default('نظام إدارة الضرائب'),
                                        Forms\Components\TextInput::make('logo_height')
                                            ->label('ارتفاع الشعار (بالبكسل)')
                                            ->numeric()
                                            ->minValue(20)
                                            ->maxValue(200)
                                            ->default(50),
                                        Forms\Components\FileUpload::make('favicon')
                                            ->label('أيقونة الموقع (Favicon)')
                                            ->image()
                                            ->directory('settings')
                                            ->acceptedFileTypes(['image/x-icon', 'image/png', 'image/jpeg'])
                                            ->maxSize(1024),
                                        Forms\Components\FileUpload::make('logo')
                                            ->label('شعار الموقع')
                                            ->image()
                                            ->directory('settings')
                                            ->acceptedFileTypes(['image/png', 'image/jpeg', 'image/svg+xml'])
                                            ->maxSize(2048),
                                    ])
                                    ->columns(2),
                            ]),
                        Forms\Components\Tabs\Tab::make('المظهر')
                            ->schema([
                                Forms\Components\Section::make('الألوان الأساسية')
                                    ->schema([
                                        Forms\Components\ColorPicker::make('primary_color')
                                            ->label('اللون الأساسي')
                                            ->default('#059669'),
                                        Forms\Components\ColorPicker::make('secondary_color')
                                            ->label('اللون الثانوي')
                                            ->default('#6B7280'),
                                        Forms\Components\ColorPicker::make('success_color')
                                            ->label('لون النجاح')
                                            ->default('#10B981'),
                                        Forms\Components\ColorPicker::make('warning_color')
                                            ->label('لون التحذير')
                                            ->default('#F59E0B'),
                                        Forms\Components\ColorPicker::make('danger_color')
                                            ->label('لون الخطر')
                                            ->default('#EF4444'),
                                        Forms\Components\ColorPicker::make('info_color')
                                            ->label('لون المعلومات')
                                            ->default('#3B82F6'),
                                    ])
                                    ->columns(3),
                            ]),
                        Forms\Components\Tabs\Tab::make('أمني')
                            ->schema([
                                Forms\Components\Section::make('إعدادات الأمان')
                                    ->schema([
                                        Forms\Components\Toggle::make('force_https')
                                            ->label('فرض استخدام HTTPS')
                                            ->default(false),
                                        Forms\Components\TextInput::make('session_timeout')
                                            ->label('انتهاء الجلسة (بالدقائق)')
                                            ->numeric()
                                            ->minValue(5)
                                            ->maxValue(1440)
                                            ->default(120),
                                        Forms\Components\TextInput::make('max_login_attempts')
                                            ->label('عدد محاولات تسجيل الدخول المسموحة')
                                            ->numeric()
                                            ->minValue(3)
                                            ->maxValue(10)
                                            ->default(5),
                                        Forms\Components\Toggle::make('enable_two_factor')
                                            ->label('تفعيل المصادقة الثنائية')
                                            ->default(false),
                                    ])
                                    ->columns(2),
                            ]),
                        Forms\Components\Tabs\Tab::make('النظام')
                            ->schema([
                                Forms\Components\Section::make('إعدادات النظام')
                                    ->schema([
                                        Forms\Components\Select::make('default_language')
                                            ->label('اللغة الافتراضية')
                                            ->options([
                                                'ar' => 'العربية',
                                                'en' => 'English',
                                            ])
                                            ->default('ar'),
                                        Forms\Components\Select::make('timezone')
                                            ->label('المنطقة الزمنية')
                                            ->options([
                                                'Africa/Cairo' => 'القاهرة',
                                                'Asia/Riyadh' => 'الرياض',
                                                'Asia/Dubai' => 'دبي',
                                            ])
                                            ->default('Africa/Cairo'),
                                        Forms\Components\TextInput::make('items_per_page')
                                            ->label('عدد العناصر في الصفحة')
                                            ->numeric()
                                            ->minValue(10)
                                            ->maxValue(100)
                                            ->default(25),
                                        Forms\Components\Toggle::make('maintenance_mode')
                                            ->label('وضع الصيانة')
                                            ->default(false),
                                    ])
                                    ->columns(2),
                            ]),
                    ])
                    ->columnSpanFull(),
            ])
            ->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('save')
                ->label('حفظ الإعدادات')
                ->submit('save'),
        ];
    }

    public function save(): void
    {
        $data = $this->form->getState();

        foreach ($data as $key => $value) {
            Setting::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'type' => $this->getFieldType($key),
                    'category' => $this->getFieldCategory($key),
                    'label' => $this->getFieldLabel($key),
                ]
            );
        }

        Notification::make()
            ->title('تم حفظ الإعدادات بنجاح')
            ->success()
            ->send();
    }

    protected function getFieldType(string $key): string
    {
        return match($key) {
            'logo_height', 'session_timeout', 'max_login_attempts', 'items_per_page' => 'integer',
            'force_https', 'enable_two_factor', 'maintenance_mode' => 'boolean',
            'primary_color', 'secondary_color', 'success_color', 'warning_color', 'danger_color', 'info_color' => 'color',
            'favicon', 'logo' => 'file',
            default => 'string',
        };
    }

    protected function getFieldCategory(string $key): string
    {
        return match($key) {
            'site_name', 'logo_height', 'favicon', 'logo' => 'general',
            'primary_color', 'secondary_color', 'success_color', 'warning_color', 'danger_color', 'info_color' => 'appearance',
            'force_https', 'session_timeout', 'max_login_attempts', 'enable_two_factor' => 'security',
            'default_language', 'timezone', 'items_per_page', 'maintenance_mode' => 'system',
            default => 'general',
        };
    }

    protected function getFieldLabel(string $key): string
    {
        return match($key) {
            'site_name' => 'اسم الموقع',
            'logo_height' => 'ارتفاع الشعار',
            'favicon' => 'أيقونة الموقع',
            'logo' => 'شعار الموقع',
            'primary_color' => 'اللون الأساسي',
            'secondary_color' => 'اللون الثانوي',
            'success_color' => 'لون النجاح',
            'warning_color' => 'لون التحذير',
            'danger_color' => 'لون الخطر',
            'info_color' => 'لون المعلومات',
            'force_https' => 'فرض HTTPS',
            'session_timeout' => 'انتهاء الجلسة',
            'max_login_attempts' => 'محاولات تسجيل الدخول',
            'enable_two_factor' => 'المصادقة الثنائية',
            'default_language' => 'اللغة الافتراضية',
            'timezone' => 'المنطقة الزمنية',
            'items_per_page' => 'عناصر الصفحة',
            'maintenance_mode' => 'وضع الصيانة',
            default => $key,
        };
    }
}
