<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Office extends Model
{
    protected $fillable = [
        'name',
        'name_ar',
        'department_id',
        'description',
        'address',
        'phone',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    public function treasuries(): HasMany
    {
        return $this->hasMany(Treasury::class);
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function taxpayerTransactions(): HasMany
    {
        return $this->hasMany(TaxpayerTransaction::class);
    }

    public function individualTransactions(): HasMany
    {
        return $this->hasMany(IndividualTransaction::class);
    }

    public function exemptTransactions(): Has<PERSON>any
    {
        return $this->hasMany(ExemptTransaction::class);
    }

    public function completedTransactions(): HasMany
    {
        return $this->hasMany(CompletedTransaction::class);
    }

    public function cancelledStickers(): HasMany
    {
        return $this->hasMany(CancelledSticker::class);
    }

    public function prePrintedReceiptSeries(): HasMany
    {
        return $this->hasMany(PrePrintedReceiptSeries::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }
}
