<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('taxpayers', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Company/Individual name
            $table->string('name_ar')->nullable();
            $table->string('type')->default('company'); // company, individual
            $table->string('tax_file_number')->nullable()->unique();
            $table->string('auto_number')->nullable()->unique(); // الرقم الآلي
            $table->string('registry_number')->nullable(); // رقم السجل
            $table->string('national_id')->nullable(); // For individuals
            $table->string('authorized_person')->nullable(); // المفوض
            $table->string('authorized_person_id')->nullable();
            $table->foreignId('legal_form_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('activity_type_id')->nullable()->constrained()->onDelete('set null');
            $table->string('address')->nullable();
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->text('notes')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['type', 'is_active']);
            $table->index(['tax_file_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('taxpayers');
    }
};
