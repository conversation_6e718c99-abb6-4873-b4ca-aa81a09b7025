<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Taxpayer extends Model
{
    protected $fillable = [
        'name',
        'name_ar',
        'type',
        'tax_file_number',
        'auto_number',
        'registry_number',
        'national_id',
        'authorized_person',
        'authorized_person_id',
        'legal_form_id',
        'tax_type_id',
        'activity_type_id',
        'address',
        'phone',
        'email',
        'notes',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function legalForm(): BelongsTo
    {
        return $this->belongsTo(LegalForm::class);
    }

    public function taxType(): BelongsTo
    {
        return $this->belongsTo(TaxType::class);
    }

    public function activityType(): BelongsTo
    {
        return $this->belongsTo(ActivityType::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeCompanies($query)
    {
        return $query->where('type', 'company');
    }

    public function scopeIndividuals($query)
    {
        return $query->where('type', 'individual');
    }

    public function scopeWithTaxFile($query)
    {
        return $query->whereNotNull('tax_file_number');
    }

    // Helper methods
    public function getDisplayNameAttribute(): string
    {
        $name = $this->name_ar ?: $this->name;

        if ($this->type === 'company' && $this->tax_file_number) {
            return $name . ' (ملف: ' . $this->tax_file_number . ')';
        }

        if ($this->type === 'individual' && $this->national_id) {
            return $name . ' (هوية: ' . $this->national_id . ')';
        }

        return $name;
    }

    public function getTypeLabelAttribute(): string
    {
        return match($this->type) {
            'company' => 'شركة',
            'individual' => 'فرد',
            default => $this->type,
        };
    }
}
