<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CompletedTransactionResource\Pages;
use App\Filament\Resources\CompletedTransactionResource\RelationManagers;
use App\Models\CompletedTransaction;
use App\Models\Department;
use App\Models\Office;
use App\Models\TransactionType;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Support\Enums\FontWeight;

class CompletedTransactionResource extends Resource
{
    protected static ?string $model = CompletedTransaction::class;

    // protected static ?string $navigationIcon = 'heroicon-o-check-circle';

    protected static ?string $navigationGroup = 'عمليات الدمغة';

    protected static ?string $modelLabel = 'معاملة مستوفية';

    protected static ?string $pluralModelLabel = 'المعاملات المستوفية';

    protected static ?int $navigationSort = 4;

    // Disable create action
    public static function canCreate(): bool
    {
        return false;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات المعاملة المستوفية')
                    ->schema([
                        Forms\Components\Select::make('department_id')
                            ->label('الإدارة')
                            ->options(Department::active()->pluck('name_ar', 'id'))
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(fn (callable $set) => $set('office_id', null)),
                        Forms\Components\Select::make('office_id')
                            ->label('المكتب')
                            ->options(function (callable $get) {
                                $departmentId = $get('department_id');
                                if (!$departmentId) return [];
                                return Office::where('department_id', $departmentId)->active()->pluck('name_ar', 'id');
                            })
                            ->required()
                            ->reactive(),
                        Forms\Components\Select::make('transaction_type_id')
                            ->label('نوع المعاملة')
                            ->options(TransactionType::active()->pluck('name_ar', 'id'))
                            ->required()
                            ->searchable(),
                        Forms\Components\Select::make('completed_by')
                            ->label('أكمل بواسطة')
                            ->options(User::active()->pluck('name', 'id'))
                            ->required()
                            ->default(auth()->id())
                            ->searchable(),
                        Forms\Components\DateTimePicker::make('completed_at')
                            ->label('تاريخ الإكمال')
                            ->required()
                            ->default(now()),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('معلومات الكيان')
                    ->schema([
                        Forms\Components\TextInput::make('entity_name')
                            ->label('اسم الكيان')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('entity_id')
                            ->label('رقم هوية الكيان')
                            ->maxLength(255),
                        Forms\Components\Select::make('entity_type')
                            ->label('نوع الكيان')
                            ->options([
                                'company' => 'شركة',
                                'individual' => 'فرد',
                            ])
                            ->required(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('المعلومات المالية')
                    ->schema([
                        Forms\Components\TextInput::make('total_amount')
                            ->label('إجمالي المبلغ')
                            ->required()
                            ->numeric()
                            ->step(0.01)
                            ->prefix('ج.م'),
                        Forms\Components\TextInput::make('paid_amount')
                            ->label('المبلغ المدفوع')
                            ->required()
                            ->numeric()
                            ->step(0.01)
                            ->prefix('ج.م'),
                        Forms\Components\Select::make('payment_method')
                            ->label('طريقة الدفع')
                            ->options([
                                'cash' => 'نقدي',
                                'electronic' => 'إلكتروني',
                                'check' => 'شيك',
                            ])
                            ->required()
                            ->default('cash'),
                        Forms\Components\TextInput::make('receipt_number')
                            ->label('رقم الإيصال')
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('تفاصيل إضافية')
                    ->schema([
                        Forms\Components\Textarea::make('completion_notes')
                            ->label('ملاحظات الإكمال')
                            ->rows(3)
                            ->columnSpanFull(),
                        Forms\Components\Hidden::make('source_type')
                            ->default('App\\Models\\TaxpayerTransaction'),
                        Forms\Components\Hidden::make('source_id'),
                        Forms\Components\Hidden::make('completion_data'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('entity_name')
                    ->label('اسم الكيان')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Bold),
                Tables\Columns\TextColumn::make('entity_type')
                    ->label('نوع الكيان')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'company' => 'شركة',
                        'individual' => 'فرد',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'company' => 'info',
                        'individual' => 'success',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('department.name_ar')
                    ->label('الإدارة')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('office.name_ar')
                    ->label('المكتب')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('transactionType.name_ar')
                    ->label('نوع المعاملة')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('total_amount')
                    ->label('إجمالي المبلغ')
                    ->numeric(decimalPlaces: 2)
                    ->sortable()
                    ->prefix('ج.م ')
                    ->color('success'),
                Tables\Columns\TextColumn::make('paid_amount')
                    ->label('المبلغ المدفوع')
                    ->numeric(decimalPlaces: 2)
                    ->sortable()
                    ->prefix('ج.م ')
                    ->color('info'),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label('طريقة الدفع')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'cash' => 'نقدي',
                        'electronic' => 'إلكتروني',
                        'check' => 'شيك',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'cash' => 'success',
                        'electronic' => 'info',
                        'check' => 'warning',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('receipt_number')
                    ->label('رقم الإيصال')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('completedBy.name')
                    ->label('أكمل بواسطة')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('completed_at')
                    ->label('تاريخ الإكمال')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('department_id')
                    ->label('الإدارة')
                    ->options(Department::active()->pluck('name_ar', 'id')),
                Tables\Filters\SelectFilter::make('office_id')
                    ->label('المكتب')
                    ->options(Office::active()->with('department')->get()->mapWithKeys(function ($office) {
                        return [$office->id => $office->department->name_ar . ' - ' . $office->name_ar];
                    })),
                Tables\Filters\SelectFilter::make('transaction_type_id')
                    ->label('نوع المعاملة')
                    ->options(TransactionType::active()->pluck('name_ar', 'id')),
                Tables\Filters\SelectFilter::make('entity_type')
                    ->label('نوع الكيان')
                    ->options([
                        'company' => 'شركة',
                        'individual' => 'فرد',
                    ]),
                Tables\Filters\SelectFilter::make('payment_method')
                    ->label('طريقة الدفع')
                    ->options([
                        'cash' => 'نقدي',
                        'electronic' => 'إلكتروني',
                        'check' => 'شيك',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () => auth()->user()->can('delete_completed_transactions')),
                ]),
            ])
            ->defaultSort('completed_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompletedTransactions::route('/'),
            'view' => Pages\ViewCompletedTransaction::route('/{record}'),
            'edit' => Pages\EditCompletedTransaction::route('/{record}/edit'),
        ];
    }
}
