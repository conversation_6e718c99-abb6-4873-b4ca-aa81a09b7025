<input
    <?php echo e($attributes
            ->merge([
                'id' => $getId(),
                'type' => 'hidden',
                $applyStateBindingModifiers('wire:model') => $getStatePath(),
            ], escape: false)
            ->merge($getExtraAttributes(), escape: false)
            ->class(['fi-fo-hidden'])); ?>

/>
<?php /**PATH F:\0_____sources\0_____tax\01\tax-dashboard\vendor\filament\forms\src\/../resources/views/components/hidden.blade.php ENDPATH**/ ?>