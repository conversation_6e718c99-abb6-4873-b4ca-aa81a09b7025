<?php

namespace App\Filament\Resources\CompletedTransactionResource\Pages;

use App\Filament\Resources\CompletedTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewCompletedTransaction extends ViewRecord
{
    protected static string $resource = CompletedTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('print')
                ->label('طباعة')
                ->icon('heroicon-o-printer')
                ->color('success')
                ->action(function () {
                    // Print functionality will be implemented later
                    $this->notify('success', 'سيتم تطوير وظيفة الطباعة قريباً');
                }),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Tabs::make('معلومات المعاملة')
                    ->tabs([
                        Infolists\Components\Tabs\Tab::make('التفاصيل')
                            ->schema([
                                Infolists\Components\Section::make('معلومات المعاملة')
                                    ->schema([
                                        Infolists\Components\TextEntry::make('entity_name')
                                            ->label('اسم الكيان'),
                                        Infolists\Components\TextEntry::make('entity_type')
                                            ->label('نوع الكيان')
                                            ->formatStateUsing(fn ($state) => match($state) {
                                                'company' => 'شركة',
                                                'individual' => 'فرد',
                                                default => $state,
                                            }),
                                        Infolists\Components\TextEntry::make('department.name_ar')
                                            ->label('الإدارة'),
                                        Infolists\Components\TextEntry::make('office.name_ar')
                                            ->label('المكتب'),
                                        Infolists\Components\TextEntry::make('transactionType.name_ar')
                                            ->label('نوع المعاملة'),
                                        Infolists\Components\TextEntry::make('total_amount')
                                            ->label('إجمالي المبلغ')
                                            ->money('EGP'),
                                        Infolists\Components\TextEntry::make('paid_amount')
                                            ->label('المبلغ المدفوع')
                                            ->money('EGP'),
                                        Infolists\Components\TextEntry::make('payment_method')
                                            ->label('طريقة الدفع')
                                            ->formatStateUsing(fn ($state) => match($state) {
                                                'cash' => 'نقدي',
                                                'electronic' => 'إلكتروني',
                                                'check' => 'شيك',
                                                default => $state,
                                            }),
                                        Infolists\Components\TextEntry::make('receipt_number')
                                            ->label('رقم الإيصال'),
                                        Infolists\Components\TextEntry::make('completed_at')
                                            ->label('تاريخ الإكمال')
                                            ->dateTime('d/m/Y H:i'),
                                        Infolists\Components\TextEntry::make('completedBy.name')
                                            ->label('أكمل بواسطة'),
                                        Infolists\Components\TextEntry::make('completion_notes')
                                            ->label('ملاحظات الإكمال')
                                            ->columnSpanFull(),
                                    ])
                                    ->columns(2),
                            ]),
                        Infolists\Components\Tabs\Tab::make('الطباعة')
                            ->schema([
                                Infolists\Components\Section::make('خيارات الطباعة')
                                    ->schema([
                                        Infolists\Components\Actions::make([
                                            Infolists\Components\Actions\Action::make('print_receipt')
                                                ->label('طباعة الإيصال')
                                                ->icon('heroicon-o-document-text')
                                                ->color('primary')
                                                ->action(fn () => $this->notify('info', 'سيتم تطوير طباعة الإيصال قريباً')),
                                            Infolists\Components\Actions\Action::make('print_tax_certificate')
                                                ->label('طباعة شهادة ضريبية')
                                                ->icon('heroicon-o-document-check')
                                                ->color('success')
                                                ->action(fn () => $this->notify('info', 'سيتم تطوير طباعة الشهادة الضريبية قريباً')),
                                            Infolists\Components\Actions\Action::make('print_detailed_report')
                                                ->label('طباعة تقرير مفصل')
                                                ->icon('heroicon-o-document-chart-bar')
                                                ->color('warning')
                                                ->action(fn () => $this->notify('info', 'سيتم تطوير طباعة التقرير المفصل قريباً')),
                                        ]),
                                    ]),
                            ]),
                    ]),
            ]);
    }
}
