<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Department;
use App\Models\Office;
use App\Models\TaxType;
use App\Models\ActivityType;
use App\Models\LegalForm;
use App\Models\TransactionType;
use App\Models\Treasury;

class SimpleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Tax Types
        $taxTypes = [
            ['name' => 'ضريبة على المهن الحرة', 'name_ar' => 'ضريبة على المهن الحرة'],
            ['name' => 'ضريبة دخل تجارة وصناعة وحرف', 'name_ar' => 'ضريبة دخل تجارة وصناعة وحرف'],
            ['name' => 'ضريبة دخل الشركات', 'name_ar' => 'ضريبة دخل الشركات'],
            ['name' => 'ضريبة على الأجور والمرتبات', 'name_ar' => 'ضريبة على الأجور والمرتبات'],
        ];

        foreach ($taxTypes as $taxType) {
            TaxType::create($taxType);
        }

        // Create Legal Forms
        $legalForms = [
            ['name' => 'قطاع عام', 'name_ar' => 'قطاع عام'],
            ['name' => 'شركة مساهمة', 'name_ar' => 'شركة مساهمة'],
            ['name' => 'فردي', 'name_ar' => 'فردي'],
            ['name' => 'شركة أجنبية', 'name_ar' => 'شركة أجنبية'],
            ['name' => 'شركة ذات مسؤولية محدودة', 'name_ar' => 'شركة ذات مسؤولية محدودة'],
        ];

        foreach ($legalForms as $legalForm) {
            LegalForm::create($legalForm);
        }

        // Create Departments
        $departments = [
            ['name' => 'الإدارة الضريبية المركزية', 'name_ar' => 'الإدارة الضريبية المركزية'],
            ['name' => 'الإدارة الضريبية الشمالية', 'name_ar' => 'الإدارة الضريبية الشمالية'],
            ['name' => 'الإدارة الضريبية الجنوبية', 'name_ar' => 'الإدارة الضريبية الجنوبية'],
        ];

        foreach ($departments as $departmentData) {
            $department = Department::create($departmentData);

            // Create offices for each department
            $offices = [
                ['name' => $departmentData['name_ar'] . ' - المكتب الرئيسي', 'name_ar' => $departmentData['name_ar'] . ' - المكتب الرئيسي'],
                ['name' => $departmentData['name_ar'] . ' - الفرع الأول', 'name_ar' => $departmentData['name_ar'] . ' - الفرع الأول'],
            ];

            foreach ($offices as $officeData) {
                $office = Office::create([
                    'name' => $officeData['name'],
                    'name_ar' => $officeData['name_ar'],
                    'department_id' => $department->id,
                    'address' => 'عنوان تجريبي لـ ' . $officeData['name_ar'],
                    'phone' => '+20-1234567890',
                ]);

                // Create treasuries for each office
                $treasuries = [
                    ['name' => 'الخزينة الرئيسية', 'name_ar' => 'الخزينة الرئيسية'],
                    ['name' => 'الخزينة الفرعية', 'name_ar' => 'الخزينة الفرعية'],
                ];

                foreach ($treasuries as $treasuryData) {
                    Treasury::create([
                        'name' => $treasuryData['name'],
                        'name_ar' => $treasuryData['name_ar'],
                        'office_id' => $office->id,
                        'current_balance' => 0,
                    ]);
                }
            }
        }

        // Create Activity Types
        $professionalTaxType = TaxType::where('name_ar', 'ضريبة على المهن الحرة')->first();
        $tradeTaxType = TaxType::where('name_ar', 'ضريبة دخل تجارة وصناعة وحرف')->first();

        $activityTypes = [
            ['name' => 'ممارسة طبية', 'name_ar' => 'ممارسة طبية', 'tax_type_id' => $professionalTaxType->id],
            ['name' => 'ممارسة قانونية', 'name_ar' => 'ممارسة قانونية', 'tax_type_id' => $professionalTaxType->id],
            ['name' => 'تجارة تجزئة', 'name_ar' => 'تجارة تجزئة', 'tax_type_id' => $tradeTaxType->id],
            ['name' => 'تصنيع', 'name_ar' => 'تصنيع', 'tax_type_id' => $tradeTaxType->id],
        ];

        foreach ($activityTypes as $activityType) {
            ActivityType::create($activityType);
        }

        // Create Transaction Types
        $transactionTypes = [
            [
                'name' => 'دفع ضريبة عادية',
                'name_ar' => 'دفع ضريبة عادية',
                'description' => 'معاملة دفع ضريبة عادية',
                'default_value' => 100.00,
                'has_tax_file' => true,
                'shows_in_stamp_report' => true,
            ],
            [
                'name' => 'ضريبة عقد',
                'name_ar' => 'ضريبة عقد',
                'description' => 'ضريبة على العقود',
                'default_value' => 0.5,
                'is_percentage' => true,
                'has_contract' => true,
                'has_parties' => true,
                'has_first_party' => true,
                'has_second_party' => true,
                'shows_in_stamp_report' => true,
            ],
            [
                'name' => 'معاملة معفاة',
                'name_ar' => 'معاملة معفاة',
                'description' => 'معاملة معفاة من الضريبة',
                'default_value' => 0.00,
                'is_tax_burden_exempt' => true,
                'has_tax_file' => true,
                'shows_in_stamp_report' => true,
            ],
        ];

        foreach ($transactionTypes as $transactionType) {
            TransactionType::create($transactionType);
        }
    }
}
