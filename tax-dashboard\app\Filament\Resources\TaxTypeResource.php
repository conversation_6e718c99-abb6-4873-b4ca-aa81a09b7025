<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TaxTypeResource\Pages;
use App\Filament\Resources\TaxTypeResource\RelationManagers;
use App\Models\TaxType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TaxTypeResource extends Resource
{
    protected static ?string $model = TaxType::class;

    // protected static ?string $navigationIcon = 'heroicon-o-scale';

    protected static ?string $navigationGroup = 'إدارة البيانات';

    protected static ?string $modelLabel = 'نوع ضريبة';

    protected static ?string $pluralModelLabel = 'أنواع الضرائب';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات نوع الضريبة')
                    ->schema([
                        Forms\Components\TextInput::make('name_ar')
                            ->label('اسم نوع الضريبة')
                            ->required()
                            ->maxLength(255)
                            ->afterStateUpdated(fn ($state, callable $set) => $set('name', $state)),
                        Forms\Components\Hidden::make('name'),
                        Forms\Components\Textarea::make('description')
                            ->label('الوصف')
                            ->rows(3)
                            ->columnSpanFull(),
                        Forms\Components\Toggle::make('is_active')
                            ->label('مفعل')
                            ->default(true),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')
                    ->label('اسم نوع الضريبة')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('activity_types_count')
                    ->label('عدد الأنشطة')
                    ->counts('activityTypes')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('الحالة')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('الحالة')
                    ->trueLabel('مفعل')
                    ->falseLabel('غير مفعل')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTaxTypes::route('/'),
            'create' => Pages\CreateTaxType::route('/create'),
            'edit' => Pages\EditTaxType::route('/{record}/edit'),
        ];
    }
}
