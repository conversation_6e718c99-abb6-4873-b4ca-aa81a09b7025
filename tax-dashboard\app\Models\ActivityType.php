<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ActivityType extends Model
{
    protected $fillable = [
        'name',
        'name_ar',
        'tax_type_id',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function taxType(): BelongsTo
    {
        return $this->belongsTo(TaxType::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForTaxType($query, $taxTypeId)
    {
        return $query->where('tax_type_id', $taxTypeId);
    }
}
