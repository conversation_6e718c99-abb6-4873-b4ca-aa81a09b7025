<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TaxpayerTransactionResource\Pages;
use App\Filament\Resources\TaxpayerTransactionResource\RelationManagers;
use App\Models\TaxpayerTransaction;
use App\Models\Department;
use App\Models\Office;
use App\Models\TransactionType;
use App\Models\LegalForm;
use App\Models\Taxpayer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Support\Enums\FontWeight;

class TaxpayerTransactionResource extends Resource
{
    protected static ?string $model = TaxpayerTransaction::class;

    // protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $navigationGroup = 'عمليات الدمغة';

    protected static ?string $modelLabel = 'معاملة ممول';

    protected static ?string $pluralModelLabel = 'معاملات الممولين';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات المعاملة')
                    ->schema([
                        Forms\Components\TextInput::make('reference_number')
                            ->label('رقم المرجع')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->default(fn () => 'TAX-' . now()->format('YmdHis')),
                        Forms\Components\Select::make('department_id')
                            ->label('الإدارة')
                            ->options(Department::active()->pluck('name_ar', 'id'))
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(fn (callable $set) => $set('office_id', null)),
                        Forms\Components\Select::make('office_id')
                            ->label('المكتب')
                            ->options(function (callable $get) {
                                $departmentId = $get('department_id');
                                if (!$departmentId) return [];
                                return Office::where('department_id', $departmentId)->active()->pluck('name_ar', 'id');
                            })
                            ->required()
                            ->reactive(),
                        Forms\Components\Select::make('transaction_type_id')
                            ->label('نوع المعاملة')
                            ->options(TransactionType::active()->where(function($query) {
                                $query->where('is_tax_burden_exempt', false);
                            })->pluck('name_ar', 'id'))
                            ->required()
                            ->reactive()
                            ->searchable()
                            ->afterStateUpdated(function (callable $set, $state) {
                                // Reset amount when transaction type changes
                                if ($state) {
                                    $transactionType = TransactionType::find($state);
                                    if ($transactionType && !$transactionType->is_percentage) {
                                        $set('amount', $transactionType->default_value);
                                    }
                                }
                            }),
                        Forms\Components\DatePicker::make('transaction_date')
                            ->label('تاريخ المعاملة')
                            ->required()
                            ->default(now()),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('معلومات الممول')
                    ->schema([
                        Forms\Components\Select::make('taxpayer_id')
                            ->label('اختيار الممول')
                            ->options(Taxpayer::active()->get()->mapWithKeys(function ($taxpayer) {
                                return [$taxpayer->id => $taxpayer->display_name];
                            }))
                            ->required()
                            ->searchable()
                            ->preload()
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, $state) {
                                if ($state) {
                                    $taxpayer = Taxpayer::find($state);
                                    if ($taxpayer) {
                                        $set('taxpayer_name', $taxpayer->name_ar ?: $taxpayer->name);
                                        $set('taxpayer_type', $taxpayer->type);
                                        $set('legal_form_id', $taxpayer->legal_form_id);
                                    }
                                }
                            })
                            ->placeholder('اختر الممول من القائمة'),
                        Forms\Components\Hidden::make('taxpayer_name'),
                        Forms\Components\Hidden::make('taxpayer_type'),
                        Forms\Components\Hidden::make('legal_form_id'),
                    ]),

                Forms\Components\Section::make('تفاصيل المعاملة')
                    ->schema([
                        Forms\Components\TextInput::make('amount')
                            ->label('المبلغ')
                            ->required()
                            ->numeric()
                            ->step(0.01)
                            ->prefix('ج.م'),
                        Forms\Components\Select::make('status')
                            ->label('الحالة')
                            ->options([
                                'draft' => 'مسودة',
                                'submitted' => 'مقدمة',
                                'completed' => 'مكتملة',
                                'cancelled' => 'ملغية',
                            ])
                            ->required()
                            ->default('draft'),
                        Forms\Components\Textarea::make('notes')
                            ->label('ملاحظات')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                // Dynamic fields based on transaction type configuration
                Forms\Components\Section::make('الحقول الديناميكية')
                    ->schema(function (callable $get) {
                        $transactionTypeId = $get('transaction_type_id');
                        if (!$transactionTypeId) return [];

                        $transactionType = TransactionType::find($transactionTypeId);
                        if (!$transactionType) return [];

                        $fields = [];
                        $dynamicFields = $transactionType->getDynamicFields();

                        foreach ($dynamicFields as $fieldName => $fieldConfig) {
                            $fieldType = $fieldConfig['type'];
                            $label = $fieldConfig['label'];
                            $required = $fieldConfig['required'] ?? false;

                            $field = match($fieldType) {
                                'text' => Forms\Components\Textarea::make("dynamic_fields.{$fieldName}")
                                    ->label($label)
                                    ->required($required)
                                    ->rows(3),

                                'string' => Forms\Components\TextInput::make("dynamic_fields.{$fieldName}")
                                    ->label($label)
                                    ->required($required)
                                    ->maxLength(255),

                                'date' => Forms\Components\DatePicker::make("dynamic_fields.{$fieldName}")
                                    ->label($label)
                                    ->required($required)
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, $state) use ($transactionType, $fieldName) {
                                        // Auto-calculate penalty if this is start_date and penalty is enabled
                                        if ($fieldName === 'start_date' && $transactionType->has_penalty && $state) {
                                            $penalty = $transactionType->calculatePenalty($state);
                                            $set('dynamic_fields.penalty_amount', $penalty);
                                        }
                                    }),

                                'integer' => Forms\Components\TextInput::make("dynamic_fields.{$fieldName}")
                                    ->label($label)
                                    ->required($required)
                                    ->numeric()
                                    ->minValue($fieldConfig['min'] ?? null)
                                    ->default($fieldConfig['default'] ?? null)
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, $state, callable $get) use ($transactionType) {
                                        // Recalculate amount when transaction count changes
                                        if ($transactionType->has_transaction_count) {
                                            $baseAmount = $get('amount') ?: $transactionType->default_value;
                                            $newAmount = $transactionType->calculateTaxAmount($baseAmount, $state ?: 1);
                                            $set('amount', $newAmount);
                                        }
                                    }),

                                'select_taxpayer' => Forms\Components\Select::make("dynamic_fields.{$fieldName}")
                                    ->label($label)
                                    ->required($required)
                                    ->options(Taxpayer::active()->get()->pluck('display_name', 'id'))
                                    ->searchable()
                                    ->preload()
                                    ->placeholder($required ? 'اختر ' . $label : 'اختياري - ' . $label)
                                    ->helperText($required ? 'هذا الحقل مطلوب' : 'هذا الحقل اختياري'),

                                'calculated' => Forms\Components\TextInput::make("dynamic_fields.{$fieldName}")
                                    ->label($label)
                                    ->disabled()
                                    ->dehydrated(false)
                                    ->numeric()
                                    ->prefix('ج.م'),

                                'repeater' => Forms\Components\Repeater::make("dynamic_fields.{$fieldName}")
                                    ->label($label)
                                    ->schema([
                                        Forms\Components\TextInput::make('value')
                                            ->label('القيمة')
                                            ->numeric()
                                            ->step(0.01)
                                            ->required(),
                                        Forms\Components\TextInput::make('description')
                                            ->label('الوصف')
                                            ->maxLength(255),
                                    ])
                                    ->columns(2)
                                    ->defaultItems(1)
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, $state, callable $get) use ($transactionType) {
                                        // Recalculate amount when multiple values change
                                        if ($transactionType->has_multiple_values && is_array($state)) {
                                            $values = array_column($state, 'value');
                                            $values = array_filter($values, 'is_numeric');
                                            $transactionCount = $get('dynamic_fields.transaction_count') ?: 1;
                                            $newAmount = $transactionType->calculateTaxAmount(0, $transactionCount, $values);
                                            $set('amount', $newAmount);
                                        }
                                    }),

                                default => Forms\Components\TextInput::make("dynamic_fields.{$fieldName}")
                                    ->label($label)
                                    ->required($required),
                            };

                            $fields[] = $field;
                        }

                        return $fields;
                    })
                    ->columns(2)
                    ->visible(fn (callable $get) => !empty($get('transaction_type_id'))),

                // Tax calculation section
                Forms\Components\Section::make('النتائج الضريبية')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Placeholder::make('stamp_tax')
                                    ->label('ضريبة الدمغة')
                                    ->content(function (callable $get) {
                                        $transactionTypeId = $get('transaction_type_id');
                                        $amount = $get('amount') ?: 0;
                                        $transactionCount = $get('dynamic_fields.transaction_count') ?: 1;
                                        $multipleValues = $get('dynamic_fields.multiple_values') ?: [];

                                        if (!$transactionTypeId) return 'ج.م 0.00';

                                        $transactionType = TransactionType::find($transactionTypeId);
                                        if (!$transactionType) return 'ج.م 0.00';

                                        $stampTax = $transactionType->calculateTaxAmount($amount, $transactionCount, array_column($multipleValues, 'value'));
                                        return 'ج.م ' . number_format($stampTax, 2);
                                    }),

                                Forms\Components\Placeholder::make('penalty_amount')
                                    ->label('غرامة التأخير')
                                    ->content(function (callable $get) {
                                        $transactionTypeId = $get('transaction_type_id');
                                        $startDate = $get('dynamic_fields.start_date');
                                        $amount = $get('amount') ?: 0;

                                        if (!$transactionTypeId || !$startDate) return 'ج.م 0.00';

                                        $transactionType = TransactionType::find($transactionTypeId);
                                        if (!$transactionType || !$transactionType->has_penalty) return 'ج.م 0.00';

                                        // Calculate penalty with new rules
                                        $startDate = \Carbon\Carbon::parse($startDate);
                                        $currentDate = now();
                                        $delayDays = $currentDate->diffInDays($startDate, false);

                                        if ($delayDays <= 70) return 'ج.م 0.00'; // No penalty for first 70 days

                                        $penaltyDays = $delayDays - 70;
                                        $dailyPenalty = $amount * 0.02; // 2% per day
                                        $totalPenalty = $dailyPenalty * $penaltyDays;

                                        // Maximum penalty is 50% of original amount
                                        $maxPenalty = $amount * 0.5;
                                        $finalPenalty = min($totalPenalty, $maxPenalty);

                                        return 'ج.م ' . number_format($finalPenalty, 2);
                                    }),

                                Forms\Components\Placeholder::make('clearance_tax')
                                    ->label('ضريبة المخالصة')
                                    ->content(function (callable $get) {
                                        $transactionTypeId = $get('transaction_type_id');
                                        $amount = $get('amount') ?: 0;

                                        if (!$transactionTypeId) return 'ج.م 0.00';

                                        $transactionType = TransactionType::find($transactionTypeId);
                                        if (!$transactionType || !$transactionType->has_clearances) return 'ج.م 0.00';

                                        // Calculate stamp tax first
                                        $stampTax = $transactionType->calculateTaxAmount($amount);

                                        // Clearance tax is 0.005 of stamp tax
                                        $clearanceTax = $stampTax * 0.005;

                                        // Round to nearest 0.5
                                        $clearanceTax = round($clearanceTax * 2) / 2;

                                        return 'ج.م ' . number_format($clearanceTax, 2);
                                    }),

                                Forms\Components\Placeholder::make('total_amount')
                                    ->label('إجمالي المبلغ المستحق')
                                    ->content(function (callable $get) {
                                        $transactionTypeId = $get('transaction_type_id');
                                        $amount = $get('amount') ?: 0;
                                        $startDate = $get('dynamic_fields.start_date');

                                        if (!$transactionTypeId) return 'ج.م 0.00';

                                        $transactionType = TransactionType::find($transactionTypeId);
                                        if (!$transactionType) return 'ج.م 0.00';

                                        // Calculate stamp tax
                                        $stampTax = $transactionType->calculateTaxAmount($amount);

                                        // Calculate penalty
                                        $penalty = 0;
                                        if ($transactionType->has_penalty && $startDate) {
                                            $startDate = \Carbon\Carbon::parse($startDate);
                                            $delayDays = now()->diffInDays($startDate, false);
                                            if ($delayDays > 70) {
                                                $penaltyDays = $delayDays - 70;
                                                $penalty = min($amount * 0.02 * $penaltyDays, $amount * 0.5);
                                            }
                                        }

                                        // Calculate clearance tax
                                        $clearanceTax = 0;
                                        if ($transactionType->has_clearances) {
                                            $clearanceTax = round(($stampTax * 0.005) * 2) / 2;
                                        }

                                        $total = $stampTax + $penalty + $clearanceTax;
                                        return 'ج.م ' . number_format($total, 2);
                                    })
                                    ->extraAttributes(['style' => 'font-weight: bold; font-size: 1.1em; color: #059669;']),
                            ]),
                    ])
                    ->visible(fn (callable $get) => !empty($get('transaction_type_id')))
                    ->collapsible(),
            ]);
    }

    public static function getFormValidationRules(): array
    {
        return [
            'dynamic_fields.third_party_taxpayer_id' => 'nullable|exists:taxpayers,id',
            'dynamic_fields.first_party_taxpayer_id' => 'nullable|exists:taxpayers,id',
            'dynamic_fields.second_party_taxpayer_id' => 'nullable|exists:taxpayers,id',
        ];
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('reference_number')
                    ->label('رقم المرجع')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Bold),
                Tables\Columns\TextColumn::make('taxpayer_name')
                    ->label('اسم الممول')
                    ->searchable()
                    ->sortable()
                    ->limit(30),
                Tables\Columns\TextColumn::make('transactionType.name_ar')
                    ->label('نوع المعاملة')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('department.name_ar')
                    ->label('الإدارة')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('office.name_ar')
                    ->label('المكتب')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('taxpayer_type')
                    ->label('نوع الممول')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'company' => 'شركة',
                        'individual' => 'فرد',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'company' => 'info',
                        'individual' => 'success',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('amount')
                    ->label('المبلغ')
                    ->numeric(decimalPlaces: 2)
                    ->sortable()
                    ->prefix('ج.م ')
                    ->color('success'),
                Tables\Columns\TextColumn::make('status')
                    ->label('الحالة')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'draft' => 'مسودة',
                        'submitted' => 'مقدمة',
                        'completed' => 'مكتملة',
                        'cancelled' => 'ملغية',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'draft' => 'gray',
                        'submitted' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('transaction_date')
                    ->label('تاريخ المعاملة')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.name')
                    ->label('المستخدم')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('department_id')
                    ->label('الإدارة')
                    ->options(Department::active()->pluck('name_ar', 'id')),
                Tables\Filters\SelectFilter::make('office_id')
                    ->label('المكتب')
                    ->options(Office::active()->with('department')->get()->mapWithKeys(function ($office) {
                        return [$office->id => $office->department->name_ar . ' - ' . $office->name_ar];
                    })),
                Tables\Filters\SelectFilter::make('transaction_type_id')
                    ->label('نوع المعاملة')
                    ->options(TransactionType::active()->pluck('name_ar', 'id')),
                Tables\Filters\SelectFilter::make('taxpayer_type')
                    ->label('نوع الممول')
                    ->options([
                        'company' => 'شركة',
                        'individual' => 'فرد',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->label('الحالة')
                    ->options([
                        'draft' => 'مسودة',
                        'submitted' => 'مقدمة',
                        'completed' => 'مكتملة',
                        'cancelled' => 'ملغية',
                    ]),
                Tables\Filters\Filter::make('transaction_date')
                    ->label('تاريخ المعاملة')
                    ->form([
                        Forms\Components\DatePicker::make('transaction_from')
                            ->label('من تاريخ'),
                        Forms\Components\DatePicker::make('transaction_until')
                            ->label('إلى تاريخ'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['transaction_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('transaction_date', '>=', $date),
                            )
                            ->when(
                                $data['transaction_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('transaction_date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => in_array($record->status, ['draft', 'submitted'])),
                Tables\Actions\Action::make('submit')
                    ->label('تقديم')
                    ->icon('heroicon-o-paper-airplane')
                    ->color('warning')
                    ->requiresConfirmation()
                    ->action(fn ($record) => $record->submit())
                    ->visible(fn ($record) => $record->status === 'draft'),
                Tables\Actions\Action::make('complete')
                    ->label('إكمال')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->action(fn ($record) => $record->complete())
                    ->visible(fn ($record) => $record->status === 'submitted'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () => auth()->user()->can('delete_taxpayer_transactions')),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->modifyQueryUsing(function (Builder $query) {
                // Default filter: show only today's transactions
                return $query->whereDate('transaction_date', today());
            });
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTaxpayerTransactions::route('/'),
            'create' => Pages\CreateTaxpayerTransaction::route('/create'),
            'edit' => Pages\EditTaxpayerTransaction::route('/{record}/edit'),
        ];
    }
}
