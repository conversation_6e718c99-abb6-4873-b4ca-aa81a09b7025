<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TreasuryResource\Pages;
use App\Filament\Resources\TreasuryResource\RelationManagers;
use App\Models\Treasury;
use App\Models\Office;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TreasuryResource extends Resource
{
    protected static ?string $model = Treasury::class;

    // protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationGroup = 'عمليات الخزينة';

    protected static ?string $modelLabel = 'خزينة';

    protected static ?string $pluralModelLabel = 'الخزائن';

    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات الخزينة')
                    ->schema([
                        Forms\Components\TextInput::make('name_ar')
                            ->label('اسم الخزينة')
                            ->required()
                            ->maxLength(255)
                            ->afterStateUpdated(fn ($state, callable $set) => $set('name', $state)),
                        Forms\Components\Hidden::make('name'),
                        Forms\Components\Select::make('office_id')
                            ->label('المكتب')
                            ->options(Office::active()->with('department')->get()->mapWithKeys(function ($office) {
                                return [$office->id => $office->department->name_ar . ' - ' . $office->name_ar];
                            }))
                            ->required()
                            ->searchable(),
                        Forms\Components\Textarea::make('description')
                            ->label('الوصف')
                            ->rows(3)
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('current_balance')
                            ->label('الرصيد الحالي')
                            ->numeric()
                            ->step(0.01)
                            ->default(0.00)
                            ->disabled()
                            ->dehydrated(false),
                        Forms\Components\Toggle::make('is_active')
                            ->label('مفعل')
                            ->default(true),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')
                    ->label('اسم الخزينة')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('office.department.name_ar')
                    ->label('الإدارة')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('office.name_ar')
                    ->label('المكتب')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('current_balance')
                    ->label('الرصيد الحالي')
                    ->numeric(decimalPlaces: 2)
                    ->sortable()
                    ->color(fn ($state) => $state >= 0 ? 'success' : 'danger'),
                Tables\Columns\TextColumn::make('users_count')
                    ->label('عدد المستخدمين')
                    ->counts('users')
                    ->sortable(),
                Tables\Columns\TextColumn::make('receipts_count')
                    ->label('عدد الإيصالات')
                    ->counts('receipts')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('الحالة')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('office_id')
                    ->label('المكتب')
                    ->options(Office::active()->with('department')->get()->mapWithKeys(function ($office) {
                        return [$office->id => $office->department->name_ar . ' - ' . $office->name_ar];
                    })),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('الحالة')
                    ->trueLabel('مفعل')
                    ->falseLabel('غير مفعل')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTreasuries::route('/'),
            'create' => Pages\CreateTreasury::route('/create'),
            'edit' => Pages\EditTreasury::route('/{record}/edit'),
        ];
    }
}
