<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Department;
use App\Models\Office;
use App\Models\TaxType;
use App\Models\ActivityType;
use App\Models\LegalForm;
use App\Models\TransactionType;
use App\Models\Treasury;
use App\Models\User;

class BasicDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Tax Types
        $taxTypes = [
            ['name' => 'ضريبة على المهن الحرة', 'name_ar' => 'ضريبة على المهن الحرة'],
            ['name' => 'ضريبة دخل تجارة وصناعة وحرف', 'name_ar' => 'ضريبة دخل تجارة وصناعة وحرف'],
            ['name' => 'ضريبة دخل الشركات', 'name_ar' => 'ضريبة دخل الشركات'],
            ['name' => 'ضريبة على الأجور والمرتبات', 'name_ar' => 'ضريبة على الأجور والمرتبات'],
        ];

        foreach ($taxTypes as $taxType) {
            TaxType::create($taxType);
        }

        // Create Legal Forms
        $legalForms = [
            ['name' => 'قطاع عام', 'name_ar' => 'قطاع عام'],
            ['name' => 'شركة مساهمة', 'name_ar' => 'شركة مساهمة'],
            ['name' => 'فردي', 'name_ar' => 'فردي'],
            ['name' => 'شركة أجنبية', 'name_ar' => 'شركة أجنبية'],
            ['name' => 'شركة ذات مسؤولية محدودة', 'name_ar' => 'شركة ذات مسؤولية محدودة'],
            ['name' => 'شركة تضامن', 'name_ar' => 'شركة تضامن'],
        ];

        foreach ($legalForms as $legalForm) {
            LegalForm::create($legalForm);
        }

        // Create Departments
        $departments = [
            ['name' => 'الإدارة الضريبية المركزية', 'name_ar' => 'الإدارة الضريبية المركزية'],
            ['name' => 'الإدارة الضريبية الشمالية', 'name_ar' => 'الإدارة الضريبية الشمالية'],
            ['name' => 'الإدارة الضريبية الجنوبية', 'name_ar' => 'الإدارة الضريبية الجنوبية'],
            ['name' => 'الإدارة الضريبية الشرقية', 'name_ar' => 'الإدارة الضريبية الشرقية'],
            ['name' => 'الإدارة الضريبية الغربية', 'name_ar' => 'الإدارة الضريبية الغربية'],
        ];

        foreach ($departments as $departmentData) {
            $department = Department::create($departmentData);

            // Create offices for each department
            $offices = [
                ['name' => $departmentData['name_ar'] . ' - المكتب الرئيسي', 'name_ar' => $departmentData['name_ar'] . ' - المكتب الرئيسي'],
                ['name' => $departmentData['name_ar'] . ' - الفرع الأول', 'name_ar' => $departmentData['name_ar'] . ' - الفرع الأول'],
                ['name' => $departmentData['name_ar'] . ' - الفرع الثاني', 'name_ar' => $departmentData['name_ar'] . ' - الفرع الثاني'],
            ];

            foreach ($offices as $officeData) {
                $office = Office::create([
                    'name' => $officeData['name'],
                    'name_ar' => $officeData['name_ar'],
                    'department_id' => $department->id,
                    'address' => 'عنوان تجريبي لـ ' . $officeData['name_ar'],
                    'phone' => '+20-1234567890',
                ]);

                // Create treasuries for each office
                $treasuries = [
                    ['name' => 'الخزينة الرئيسية', 'name_ar' => 'الخزينة الرئيسية'],
                    ['name' => 'الخزينة الفرعية', 'name_ar' => 'الخزينة الفرعية'],
                ];

                foreach ($treasuries as $treasuryData) {
                    Treasury::create([
                        'name' => $treasuryData['name'],
                        'name_ar' => $treasuryData['name_ar'],
                        'office_id' => $office->id,
                        'current_balance' => 0,
                    ]);
                }
            }
        }

        // Create Activity Types
        $professionalTaxType = TaxType::where('name_ar', 'ضريبة على المهن الحرة')->first();
        $tradeTaxType = TaxType::where('name_ar', 'ضريبة دخل تجارة وصناعة وحرف')->first();
        $corporateTaxType = TaxType::where('name_ar', 'ضريبة دخل الشركات')->first();
        $salaryTaxType = TaxType::where('name_ar', 'ضريبة على الأجور والمرتبات')->first();

        $activityTypes = [
            ['name' => 'ممارسة طبية', 'name_ar' => 'ممارسة طبية', 'tax_type_id' => $professionalTaxType->id],
            ['name' => 'ممارسة قانونية', 'name_ar' => 'ممارسة قانونية', 'tax_type_id' => $professionalTaxType->id],
            ['name' => 'استشارات هندسية', 'name_ar' => 'استشارات هندسية', 'tax_type_id' => $professionalTaxType->id],
            ['name' => 'تجارة تجزئة', 'name_ar' => 'تجارة تجزئة', 'tax_type_id' => $tradeTaxType->id],
            ['name' => 'تصنيع', 'name_ar' => 'تصنيع', 'tax_type_id' => $tradeTaxType->id],
            ['name' => 'إنشاءات', 'name_ar' => 'إنشاءات', 'tax_type_id' => $tradeTaxType->id],
            ['name' => 'خدمات شركات', 'name_ar' => 'خدمات شركات', 'tax_type_id' => $corporateTaxType->id],
            ['name' => 'خدمات مالية', 'name_ar' => 'خدمات مالية', 'tax_type_id' => $corporateTaxType->id],
            ['name' => 'رواتب موظفين', 'name_ar' => 'رواتب موظفين', 'tax_type_id' => $salaryTaxType->id],
        ];

        foreach ($activityTypes as $activityType) {
            ActivityType::create($activityType);
        }

        // Create Transaction Types with dynamic field configurations
        $transactionTypes = [
            [
                'name' => 'دفع ضريبة عادية',
                'name_ar' => 'دفع ضريبة عادية',
                'description' => 'معاملة دفع ضريبة عادية',
                'default_value' => 100.00,
                'is_percentage' => false,
                'has_tax_file' => true,
                'has_start_date' => true,
                'has_end_date' => true,
                'shows_in_stamp_report' => true,
            ],
            [
                'name' => 'ضريبة عقد',
                'name_ar' => 'ضريبة عقد',
                'description' => 'ضريبة على العقود',
                'default_value' => 0.5,
                'is_percentage' => true,
                'has_contract' => true,
                'has_parties' => true,
                'has_first_party' => true,
                'has_second_party' => true,
                'shows_in_stamp_report' => true,
            ],
            [
                'name' => 'دفع غرامة',
                'name_ar' => 'دفع غرامة',
                'description' => 'دفع غرامة تأخير',
                'default_value' => 50.00,
                'has_penalty' => true,
                'has_tax_file' => true,
                'shows_in_stamp_report' => false,
            ],
            [
                'name' => 'معاملة معفاة',
                'name_ar' => 'معاملة معفاة',
                'description' => 'معاملة معفاة من الضريبة',
                'default_value' => 0.00,
                'is_tax_burden_exempt' => true,
                'has_tax_file' => true,
                'shows_in_stamp_report' => true,
            ],
            [
                'name' => 'ضريبة كمبيالة',
                'name_ar' => 'ضريبة كمبيالة',
                'description' => 'ضريبة على الكمبيالات',
                'default_value' => 1.00,
                'has_promissory_note' => true,
                'has_parties' => true,
                'has_first_party' => true,
                'has_second_party' => true,
                'has_multiple_values' => true,
                'shows_in_stamp_report' => true,
            ],
        ];

        foreach ($transactionTypes as $transactionType) {
            TransactionType::create($transactionType);
        }

        // Create admin user
        $centralDepartment = Department::where('name_ar', 'الإدارة الضريبية المركزية')->first();
        $mainOffice = $centralDepartment->offices()->where('name_ar', 'like', '%المكتب الرئيسي%')->first();
        $mainTreasury = $mainOffice->treasuries()->where('name_ar', 'الخزينة الرئيسية')->first();

        $adminUser = User::create([
            'name' => 'مدير النظام',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'department_id' => $centralDepartment->id,
            'office_id' => $mainOffice->id,
            'treasury_id' => $mainTreasury->id,
            'employee_id' => 'EMP001',
            'phone' => '+20-1234567890',
            'is_active' => true,
        ]);

        $adminUser->assignRole('super-admin');

        // Create department admin user
        $deptAdminUser = User::create([
            'name' => 'مدير الإدارة',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'department_id' => $centralDepartment->id,
            'office_id' => $mainOffice->id,
            'treasury_id' => $mainTreasury->id,
            'employee_id' => 'EMP002',
            'phone' => '+20-1234567891',
            'is_active' => true,
        ]);

        $deptAdminUser->assignRole('department-admin');
    }
}
