<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TaxpayerResource\Pages;
use App\Filament\Resources\TaxpayerResource\RelationManagers;
use App\Models\Taxpayer;
use App\Models\LegalForm;
use App\Models\ActivityType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Support\Enums\FontWeight;

class TaxpayerResource extends Resource
{
    protected static ?string $model = Taxpayer::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    // protected static ?string $navigationGroup = 'إدارة البيانات';

    protected static ?string $modelLabel = 'ممول';

    protected static ?string $pluralModelLabel = 'الممولين';

    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('المعلومات الأساسية')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('الاسم')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('name_ar')
                            ->label('الاسم بالعربية')
                            ->maxLength(255),
                        Forms\Components\Select::make('type')
                            ->label('النوع')
                            ->options([
                                'company' => 'شركة',
                                'individual' => 'فرد',
                            ])
                            ->required()
                            ->default('company')
                            ->reactive(),
                        Forms\Components\Toggle::make('is_active')
                            ->label('مفعل')
                            ->default(true),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('معلومات الشركة')
                    ->schema([
                        Forms\Components\TextInput::make('tax_file_number')
                            ->label('رقم الملف الضريبي')
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        Forms\Components\TextInput::make('auto_number')
                            ->label('الرقم الآلي')
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        Forms\Components\TextInput::make('registry_number')
                            ->label('رقم السجل')
                            ->maxLength(255),
                        Forms\Components\Select::make('legal_form_id')
                            ->label('الشكل القانوني')
                            ->options(LegalForm::active()->pluck('name_ar', 'id'))
                            ->searchable(),
                        Forms\Components\TextInput::make('authorized_person')
                            ->label('المفوض')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('authorized_person_id')
                            ->label('رقم هوية المفوض')
                            ->maxLength(255),
                    ])
                    ->columns(2)
                    ->visible(fn (callable $get) => $get('type') === 'company'),

                Forms\Components\Section::make('معلومات الفرد')
                    ->schema([
                        Forms\Components\TextInput::make('national_id')
                            ->label('رقم الهوية الوطنية')
                            ->maxLength(255),
                    ])
                    ->visible(fn (callable $get) => $get('type') === 'individual'),

                Forms\Components\Section::make('معلومات النشاط')
                    ->schema([
                        Forms\Components\Select::make('tax_type_id')
                            ->label('نوع الضريبة')
                            ->options(TaxType::active()->pluck('name_ar', 'id'))
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(fn (callable $set) => $set('activity_type_id', null))
                            ->searchable(),
                        Forms\Components\Select::make('activity_type_id')
                            ->label('نوع النشاط')
                            ->options(function (callable $get) {
                                $taxTypeId = $get('tax_type_id');
                                if (!$taxTypeId) return [];
                                return ActivityType::where('tax_type_id', $taxTypeId)->active()->pluck('name_ar', 'id');
                            })
                            ->required()
                            ->searchable(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('معلومات الاتصال')
                    ->schema([
                        Forms\Components\Textarea::make('address')
                            ->label('العنوان')
                            ->rows(2)
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('phone')
                            ->label('رقم الهاتف')
                            ->tel()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->label('البريد الإلكتروني')
                            ->email()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('notes')
                            ->label('ملاحظات')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')
                    ->label('الاسم')
                    ->searchable()
                    ->sortable()
                    ->weight(FontWeight::Bold),
                Tables\Columns\TextColumn::make('type')
                    ->label('النوع')
                    ->formatStateUsing(fn ($state) => match($state) {
                        'company' => 'شركة',
                        'individual' => 'فرد',
                        default => $state,
                    })
                    ->badge()
                    ->color(fn ($state) => match($state) {
                        'company' => 'info',
                        'individual' => 'success',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('tax_file_number')
                    ->label('رقم الملف الضريبي')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('national_id')
                    ->label('رقم الهوية')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('legalForm.name_ar')
                    ->label('الشكل القانوني')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('activityType.name_ar')
                    ->label('نوع النشاط')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('authorized_person')
                    ->label('المفوض')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label('الهاتف')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('الحالة')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('النوع')
                    ->options([
                        'company' => 'شركة',
                        'individual' => 'فرد',
                    ]),
                Tables\Filters\SelectFilter::make('legal_form_id')
                    ->label('الشكل القانوني')
                    ->options(LegalForm::active()->pluck('name_ar', 'id')),
                Tables\Filters\SelectFilter::make('activity_type_id')
                    ->label('نوع النشاط')
                    ->options(ActivityType::active()->pluck('name_ar', 'id')),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('الحالة')
                    ->trueLabel('مفعل')
                    ->falseLabel('غير مفعل')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('toggle_status')
                    ->label(fn ($record) => $record->is_active ? 'إلغاء التفعيل' : 'تفعيل')
                    ->icon(fn ($record) => $record->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn ($record) => $record->is_active ? 'danger' : 'success')
                    ->requiresConfirmation()
                    ->action(fn ($record) => $record->update(['is_active' => !$record->is_active])),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () => auth()->user()->can('delete_taxpayers')),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTaxpayers::route('/'),
            'create' => Pages\CreateTaxpayer::route('/create'),
            'edit' => Pages\EditTaxpayer::route('/{record}/edit'),
        ];
    }
}
